#!/usr/bin/env python3

import sys
sys.path.append('.')

from optitrack_dashboard import Optitrack<PERSON>ni<PERSON>Dashboard

def test_csv_loading():
    """Test CSV loading functionality"""
    print("Testing OptiTrack Dashboard CSV Loading...")
    
    try:
        # Create dashboard instance
        dashboard = OptitrackAnimationDashboard()
        
        # Test loading the CSV file
        file_path = "data/Take 2025-06-04 11.57.30 AM_ABDADD2.csv"
        print(f"Loading file: {file_path}")
        
        dashboard.load_csv(file_path, 'dataset1')
        
        # Check results
        dataset = dashboard.datasets['dataset1']
        print(f"✅ Successfully loaded dataset!")
        print(f"📊 Format: {dataset['format_type']}")
        print(f"📊 Points: {len(dataset['point_names'])}")
        print(f"📊 Frames: {dataset['frame_count']}")
        print(f"📊 Sampling rate: {dataset['sampling_rate']:.1f} Hz")
        print(f"📊 Duration: {dataset['duration']:.2f}s")
        print(f"🎯 Point names: {', '.join(dataset['point_names'][:5])}...")
        
        # Test a few data points
        if dataset['point_data']:
            first_point = dataset['point_names'][0]
            print(f"\n📍 Sample data for {first_point}:")
            for i in range(min(3, len(dataset['point_data'][first_point]['x']))):
                x = dataset['point_data'][first_point]['x'][i]
                y = dataset['point_data'][first_point]['y'][i]
                z = dataset['point_data'][first_point]['z'][i]
                print(f"  Frame {i}: ({x:.3f}, {y:.3f}, {z:.3f})")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_csv_loading()
    if success:
        print("\n🎉 CSV loading test passed!")
    else:
        print("\n💥 CSV loading test failed!")
