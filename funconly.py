import dash
from dash import dcc, html
from dash.dependencies import Input, Output, State
import plotly.graph_objs as go
from collections import deque
import time
import random
import threading
import serial
from xbee import XBee
import csv  
import signal
import sys

# Serial parameters
serial_port = "COM6"
baudrate = 57600
serial_conn = None
xbee = None
mock = False  # Set to True for simulated data

# Define units for each sensor (updated based on new payload structure)
sensor_units = {
    "Angular Velocity": "deg/s",
    "Angular Velocity X SD": "deg/s",
    "Angular Velocity Y SD": "deg/s",
    "Angular Velocity Z SD": "deg/s",
    "Accelerometer X": "m/s²",
    "Accelerometer Y": "m/s²",
    "Accelerometer Z": "m/s²",
}

# Define mapping from sensor names to graph IDs to ensure consistency
graph_ids = {
    "Angular Velocity": "graph-angular-velocity",
    "Angular Velocity X SD": "graph-angular-velocity-x-sd",
    "Angular Velocity Y SD": "graph-angular-velocity-y-sd",
    "Angular Velocity Z SD": "graph-angular-velocity-z-sd",
    "Accelerometer X": "graph-accelerometer-x",
    "Accelerometer Y": "graph-accelerometer-y",
    "Accelerometer Z": "graph-accelerometer-z",
}

# Data storage
max_points = 1000
time_data = deque(maxlen=max_points)  # Time axis
streams = {
    "Angular Velocity": deque(maxlen=max_points),
    "Angular Velocity X SD": deque(maxlen=max_points),
    "Angular Velocity Y SD": deque(maxlen=max_points),
    "Angular Velocity Z SD": deque(maxlen=max_points),
    "Accelerometer X": deque(maxlen=max_points),
    "Accelerometer Y": deque(maxlen=max_points),
    "Accelerometer Z": deque(maxlen=max_points),
}
saved_data = []  # To store all collected data
running = True
data_thread = None
app = dash.Dash(__name__)

def signal_handler(sig, frame):
    """Handle keyboard interrupts (Ctrl+C)"""
    print("\nReceived keyboard interrupt, stopping gracefully...")
    stop()
    sys.exit(0)

def connect_serial():
    """Initialize serial connection with XBee. Returns True if successful, False otherwise."""
    global serial_conn, xbee
    try:
        serial_conn = serial.Serial(
            baudrate=baudrate,
            bytesize=8,
            port=serial_port,
            timeout=5
        )
        xbee = XBee(serial_conn)
        print("Serial connection established.")
        return True
    except Exception as e:
        print("Error connecting to serial port:", e)
        # Reset these to None to be safe
        serial_conn = None
        xbee = None
        return False

def convert_to_signed_int(unsigned_value):
    """Convert 16-bit unsigned integer to signed integer."""
    # Check if the value is already within the signed range
    if unsigned_value <= 0x7FFF:
        return unsigned_value
    # Otherwise convert using two's complement
    return unsigned_value - 0x10000

def serial_read():
    """Reads data from XBee and processes it according to the new payload structure."""
    global mock, xbee, data_thread
    try:
        # Check if XBee is initialized before trying to read
        if xbee is None:
            print("XBee not initialized. Stopping serial reader.")
            mock = True
            # Switch to mock data thread
            if running:
                data_thread = threading.Thread(target=mock_reader_thread, daemon=True)
                data_thread.start()
            return
                
        raw_data = xbee.wait_read_frame()
        data = raw_data["rf_data"]
        hex_data = data.hex()
        data_bytes = [hex_data[i:i + 2] for i in range(0, len(hex_data), 2)]
        data_decimals = [int(byte, 16) for byte in data_bytes]
        
        # Check header (first two bytes should be the same value)
        if len(data_decimals) < 20 or data_decimals[0] != data_decimals[1]:
            print("Invalid packet format: Header mismatch or incomplete data")
            return
                
        # Extract runtime from bytes 2-5 (runtime is 32-bit)
        run_time = (data_decimals[2] << 24 |
                    data_decimals[3] << 16 |
                    data_decimals[4] << 8 |
                    data_decimals[5])
        
        # Extract sensor values based on the specified payload structure
        # Each value is 16-bit (high byte then low byte)
        unsigned_angular_velocity = (data_decimals[6] << 8 | data_decimals[7])
        unsigned_angular_velocity_x_sd = (data_decimals[8] << 8 | data_decimals[9])
        unsigned_angular_velocity_y_sd = (data_decimals[10] << 8 | data_decimals[11])
        unsigned_angular_velocity_z_sd = (data_decimals[12] << 8 | data_decimals[13])
        unsigned_accelerometer_x = (data_decimals[14] << 8 | data_decimals[15])
        unsigned_accelerometer_y = (data_decimals[16] << 8 | data_decimals[17])
        unsigned_accelerometer_z = (data_decimals[18] << 8 | data_decimals[19])
        
        # Convert to signed integers
        angular_velocity = convert_to_signed_int(unsigned_angular_velocity)
        angular_velocity_x_sd = convert_to_signed_int(unsigned_angular_velocity_x_sd)
        angular_velocity_y_sd = convert_to_signed_int(unsigned_angular_velocity_y_sd)
        angular_velocity_z_sd = convert_to_signed_int(unsigned_angular_velocity_z_sd)
        
        # Convert accelerometer values to signed and apply scaling
        accelerometer_x = convert_to_signed_int(unsigned_accelerometer_x) / 100
        accelerometer_y = convert_to_signed_int(unsigned_accelerometer_y) / 100
        accelerometer_z = convert_to_signed_int(unsigned_accelerometer_z) / 100
        
        # Create processed data array
        processed_data = [
            run_time,
            angular_velocity,
            angular_velocity_x_sd,
            angular_velocity_y_sd,
            angular_velocity_z_sd,
            accelerometer_x,
            accelerometer_y,
            accelerometer_z
        ]
        
        # Process the data
        process_data(processed_data)

    except Exception as e:
        if running:  # Only print error if we're still supposed to be running
            print("Error reading serial data:", e)
            # If we can't read from XBee, switch to mock mode
            print("Switching to mock data mode after serial error.")

            mock = True
            xbee = None
            if serial_conn:
                try:
                    serial_conn.close()
                except:
                    pass

                serial_conn = None
            
            # Start mock thread if we're still running
            data_thread = threading.Thread(target=mock_reader_thread, daemon=True)
            data_thread.start()
            # Exit this thread
            return

def mock_serial_read():
    """Simulates data for testing without hardware."""
    if not running:
        return
        
    runtime = len(time_data) + 1 if time_data else 0
    # Generate some realistic mock data including negative values
    angular_velocity = random.randint(-100, 100)
    angular_velocity_x_sd = random.randint(-100, 100)
    angular_velocity_y_sd = random.randint(-100, 100)
    angular_velocity_z_sd = random.randint(-100, 100)
    accelerometer_x = random.uniform(-2.0, 2.0)
    accelerometer_y = random.uniform(-2.0, 2.0)
    accelerometer_z = random.uniform(8.0, 11.0)  # Gravity is around 9.8
    
    data = [
        runtime,
        angular_velocity,
        angular_velocity_x_sd,
        angular_velocity_y_sd,
        angular_velocity_z_sd,
        accelerometer_x,
        accelerometer_y,
        accelerometer_z
    ]
    
    process_data(data)
    time.sleep(0.1)  # Simulate delay in data collection

def process_data(data):
    """Process the incoming data and update data structures."""
    if not running:
        return
        
    # Save to permanent storage
    saved_data.append(data)
    
    # Update live data
    time_data.append(data[0])
    for i, key in enumerate(streams.keys()):
        if i + 1 < len(data):  # Make sure we have enough data points
            streams[key].append(data[i + 1])
        else:
            streams[key].append(0)  # Default value if data is missing

def serial_reader_thread():
    """Thread for reading serial data."""
    while running:
        try:
            # If we've switched to mock mode, exit this thread
            if mock or xbee is None:
                print("Serial connection not available. Exiting serial reader thread.")
                return
            
            serial_read()
        except Exception as e:
            if running:  # Only print error if we're still supposed to be running
                print(f"Error in serial reader thread: {e}")
                time.sleep(1)  # Prevent error flooding

def mock_reader_thread():
    """Thread for generating mock data."""
    max_runtime = 60000  # Auto-stop after 60 seconds for mock data (optional)
    while running:
        try:
            # If we've switched back to real mode, exit this thread
            if not mock:
                print("Switched to real data mode. Exiting mock reader thread.")
                return
                
            mock_serial_read()
            
            # Optional: Check if we should auto-stop based on data runtime
            if time_data and time_data[-1] > max_runtime and auto_stop_enabled:
                print(f"Auto-stopping after reaching {max_runtime}ms of data")
                stop()
                break
        except Exception as e:
            if running:
                print(f"Error in mock data thread: {e}")
                time.sleep(1)  # Prevent error flooding

def save_to_file():
    """Saves all collected data to a CSV file."""
    try:
        # Generate a timestamp for the filename
        timestamp = time.strftime("%Y%m%d-%H%M%S")
        filename = f"data_log_{timestamp}.csv"
        
        with open(filename, "w", newline='') as file:
            writer = csv.writer(file)
            # Write header
            header = ["Runtime"] + list(streams.keys())
            writer.writerow(header)
            # Write data
            for data in saved_data:
                writer.writerow(data)
        print(f"Data saved to {filename}")
        return filename
    except Exception as e:
        print(f"Error saving data: {e}")
        return None

def stop():
    """Stops the program gracefully."""
    global running
    if not running:
        print("Already stopped.")
        return
        
    print("Stopping data collection...")
    running = False
    
    # Give threads a moment to finish
    time.sleep(0.5)
    
    # Close serial connection if it exists
    if serial_conn:
        try:
            serial_conn.close()
            print("Serial connection closed.")
        except Exception as e:
            print(f"Error closing serial connection: {e}")
    
    # Save collected data
    filename = save_to_file()
    if filename:
        print(f"Program stopped gracefully. Data saved to {filename}")
        return filename
    else:
        print("Program stopped, but data could not be saved.")
        return None

def create_figure(sensor):
    """Create a Plotly figure for the given sensor."""
    # Different colors for different sensor types
    color_map = {
        "Angular Velocity": "#2980b9",
        "Angular Velocity X SD": "#c0392b", 
        "Angular Velocity Y SD": "#d35400",
        "Angular Velocity Z SD": "#e74c3c",
        "Accelerometer X": "#27ae60",
        "Accelerometer Y": "#2ecc71", 
        "Accelerometer Z": "#16a085"
    }
    
    color = color_map.get(sensor, "#2980b9")
    
    return {
        'data': [
            {
                'x': list(time_data),
                'y': list(streams[sensor]),
                'type': 'scatter',
                'mode': 'lines',
                'name': sensor,
                'line': {'color': color, 'width': 2}
            }
        ],
        'layout': {
            'xaxis': {
                'title': 'Time (ms)',
                'tickformat': ',d'  # Display full integers with commas as thousands separators
            },
            'yaxis': {
                'title': f'Value ({sensor_units[sensor]})',
                'tickformat': ',d' if 'Angular Velocity' in sensor else '.2f'  # Format differently based on sensor type
            },
            'margin': {'l': 50, 'r': 20, 't': 10, 'b': 50},  # Reduced top margin since title is now above
            'height': 300,
            'paper_bgcolor': 'rgba(0,0,0,0)',
            'plot_bgcolor': 'rgba(240,240,240,0.8)',
            'hovermode': 'closest',
            'uirevision': sensor,  # Keeps zoom level consistent on updates
            'hoverlabel': {
                'bgcolor': 'white',
                'font': {'size': 12}
            },
            'hoverformat': '.2f',  # Format for hover text (2 decimal places)
        }
    }

def setup_layout():
    """Set up the Dash app layout for the new sensor data structure."""
    app.layout = html.Div([
        html.H1("XBee IMU Sensor Data Visualization", 
                style={'textAlign': 'center', 'color': '#2c3e50', 'margin-bottom': '30px'}),
        
        # Dashboard information
        html.Div([
            html.H3("Dashboard Information", style={'color': '#2c3e50'}),
            html.Div([
                html.P(f"Sensor: {sensor} ({unit})", style={'margin': '5px 0'})
                for sensor, unit in sensor_units.items()
            ]),
            html.P("Values displayed as properly signed readings from the sensor",
                  style={'fontStyle': 'italic', 'marginTop': '10px'}),
            html.P(f"Data Source: {'Mock Data (Simulated)' if mock else 'Real XBee Sensor'}",
                  style={'fontWeight': 'bold', 'marginTop': '10px', 'color': '#e74c3c' if mock else '#27ae60'})
        ], style={'marginBottom': '20px', 'backgroundColor': '#f8f9fa', 'padding': '15px', 'borderRadius': '5px'}),
        
        html.Div([
            # First row: Angular Velocity
            html.Div([
                html.Div([
                    html.H3(f"Angular Velocity ({sensor_units['Angular Velocity']})", 
                            style={'textAlign': 'center', 'marginBottom': '5px'}),
                    dcc.Graph(id=graph_ids['Angular Velocity'])
                ], style={'width': '100%', 'padding': '10px'}),
            ], style={'display': 'flex', 'flexWrap': 'wrap'}),
            
            # Second row: Angular Velocity Standard Deviations
            html.Div([
                html.Div([
                    html.H3(f"Angular Velocity X SD ({sensor_units['Angular Velocity X SD']})", 
                            style={'textAlign': 'center', 'marginBottom': '5px'}),
                    dcc.Graph(id=graph_ids['Angular Velocity X SD'])
                ], style={'width': '100%', 'padding': '10px'}),
                
                html.Div([
                    html.H3(f"Angular Velocity Y SD ({sensor_units['Angular Velocity Y SD']})", 
                            style={'textAlign': 'center', 'marginBottom': '5px'}),
                    dcc.Graph(id=graph_ids['Angular Velocity Y SD'])
                ], style={'width': '100%', 'padding': '10px'}),
                
                html.Div([
                    html.H3(f"Angular Velocity Z SD ({sensor_units['Angular Velocity Z SD']})", 
                            style={'textAlign': 'center', 'marginBottom': '5px'}),
                    dcc.Graph(id=graph_ids['Angular Velocity Z SD'])
                ], style={'width': '100%', 'padding': '10px'}),
            ], style={'display': 'flex', 'flexWrap': 'wrap'}),
            
            # Third row: Accelerometer data
            html.Div([
                html.Div([
                    html.H3(f"Accelerometer X ({sensor_units['Accelerometer X']})", 
                            style={'textAlign': 'center', 'marginBottom': '5px'}),
                    dcc.Graph(id=graph_ids['Accelerometer X'])
                ], style={'width': '100%', 'padding': '10px'}),
                
                html.Div([
                    html.H3(f"Accelerometer Y ({sensor_units['Accelerometer Y']})", 
                            style={'textAlign': 'center', 'marginBottom': '5px'}),
                    dcc.Graph(id=graph_ids['Accelerometer Y'])
                ], style={'width': '100%', 'padding': '10px'}),
                
                html.Div([
                    html.H3(f"Accelerometer Z ({sensor_units['Accelerometer Z']})", 
                            style={'textAlign': 'center', 'marginBottom': '5px'}),
                    dcc.Graph(id=graph_ids['Accelerometer Z'])
                ], style={'width': '100%', 'padding': '10px'}),
            ], style={'display': 'flex', 'flexWrap': 'wrap'}),
        ]),
        
        html.Div([
            html.Button('Stop and Save Data', id='stop-button', 
                        style={'backgroundColor': '#e74c3c', 'color': 'white', 
                               'padding': '10px 20px', 'margin': '20px', 'border': 'none',
                               'borderRadius': '5px', 'fontSize': '16px', 'cursor': 'pointer'}),
            html.Div(id='save-status', style={'margin': '20px', 'color': '#27ae60'})
        ], style={'textAlign': 'center'}),
        
        dcc.Interval(
            id='interval-component',
            interval=100,  # in milliseconds
            n_intervals=0
        )
    ], style={'fontFamily': 'Arial', 'margin': '0 auto', 'maxWidth': '1400px'})

def setup_callbacks():
    """Set up the Dash app callbacks with explicit graph IDs."""
    # Create update callback for each graph
    for sensor_name, graph_id in graph_ids.items():
        @app.callback(
            Output(graph_id, 'figure'),
            [Input('interval-component', 'n_intervals')]
        )
        def update_graph_for_sensor(n_intervals, sensor=sensor_name):
            return create_figure(sensor)
    
    # Stop button callback
    @app.callback(
        Output('save-status', 'children'),
        [Input('stop-button', 'n_clicks')],
        [State('save-status', 'children')]
    )
    def stop_and_save(n_clicks, status):
        # Check if this is an initial call or an actual button click
        if n_clicks is None or n_clicks == 0:
            return status
        
        # Check if we're already stopped
        if not running:
            return "Data collection already stopped."
            
        try:
            filename = stop()
            if filename:
                return f"Data collection stopped. Data saved to {filename}"
            else:
                return "Data collection stopped, but there was an error saving the data."
        except Exception as e:
            return f"Error stopping data collection: {str(e)}"

def main(use_mock=False, runtime_seconds=None):
    """Main function to start the visualization."""
    # global mock, data_thread, running, auto_stop_enabled
    
    # Setup parameters
    # mock = use_mock
    #auto_stop_enabled = False
    # running = True
    
    # Set up signal handler for graceful shutdown
    signal.signal(signal.SIGINT, signal_handler)
    
    # Start data collection thread
    if not mock:
        # Try to connect to serial, if it fails, switch to mock mode
        if not connect_serial():
            mock = True
            print("Failed to connect to serial port, switching to mock data mode.")
            data_thread = threading.Thread(target=mock_reader_thread, daemon=True)
        else:
            data_thread = threading.Thread(target=serial_reader_thread, daemon=True)
    else:
        data_thread = threading.Thread(target=mock_reader_thread, daemon=True)
    
    data_thread.start()
    
    # Optional auto-stop timer
    if runtime_seconds:
        print(f"Auto-stop set for {runtime_seconds} seconds")
        stop_timer = threading.Timer(runtime_seconds, stop)
        stop_timer.daemon = True
        stop_timer.start()
    
    # Setup Dash application
    setup_layout()
    setup_callbacks()
    
    # Start the server
    print(f"Starting visualization server {'with mock data' if mock else ''}")
    print("Press Ctrl+C to stop the server or use the 'Stop and Save Data' button in the UI")
    try:
        app.run(debug=False)
    except KeyboardInterrupt:
        print("\nKeyboard interrupt received. Stopping...")
        stop()
    except Exception as e:
        print(f"Error in Dash server: {e}")
        stop()
    finally:
        # Ensure resources are cleaned up
        if running:
            stop()

if __name__ == "__main__":
    # Set mock=True to use simulated data, False to use real XBee data
    # Optional: Set runtime_seconds to auto-stop after a specific duration
    main(use_mock=False, runtime_seconds=None)  # Set use_mock=True for testing
    
    # You can also enable auto-stop after reaching max_runtime in mock mode
    # auto_stop_enabled = True