import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from scipy import signal
from scipy.ndimage import gaussian_filter1d
import argparse
from dash import Dash, dcc, html, Input, Output, State

class KinetisenseFilterApp:
    def __init__(self):
        self.app = Dash(__name__)
        self.data = None
        self.point_names = []
        self.filtered_data = {}
        self.setup_layout()
        self.setup_callbacks()
    
    def load_kinetisense_csv(self, file_path):
        """Load Kinetisense CSV file and parse skeletal data"""
        df = pd.read_csv(file_path)
        df = df.dropna(how='all')

        # Extract point names from headers
        headers = df.columns.tolist()
        self.point_names = []

        for header in headers:
            if header.endswith(' X'):
                point_name = header[:-2]  # Remove ' X'
                self.point_names.append(point_name)

        # Remove timestamp and primary value columns if they exist
        self.point_names = [name for name in self.point_names if name not in ['Primary Value', 'Timestamp']]

        # Initialize data structure
        self.data = {name: {'x': [], 'y': [], 'z': []} for name in self.point_names}
        timestamps = []

        # Parse data for each frame
        frame_count = 0
        for _, row in df.iterrows():
            frame_valid = False

            # Extract timestamp if available
            timestamp = None
            if 'Timestamp' in row:
                timestamp = row['Timestamp']
            elif 'Time' in row:
                timestamp = row['Time']
            else:
                # Assume 30 FPS if no timestamp
                timestamp = frame_count / 30.0

            for point_name in self.point_names:
                x_col = f"{point_name} X"
                y_col = f"{point_name} Y"
                z_col = f"{point_name} Z"

                if x_col in row and y_col in row and z_col in row:
                    x_val = row[x_col]
                    y_val = row[y_col]
                    z_val = row[z_col]

                    if pd.notna(x_val) and pd.notna(y_val) and pd.notna(z_val):
                        self.data[point_name]['x'].append(float(x_val))
                        self.data[point_name]['y'].append(float(y_val))
                        self.data[point_name]['z'].append(float(z_val))
                        frame_valid = True
                    else:
                        self.data[point_name]['x'].append(None)
                        self.data[point_name]['y'].append(None)
                        self.data[point_name]['z'].append(None)

            if frame_valid:
                timestamps.append(timestamp)
                frame_count += 1

        print(f"Loaded Kinetisense: {frame_count} frames with {len(self.point_names)} points")
        return self.data
    
    def apply_moving_average(self, data, window_size=5):
        """Apply moving average filter to data"""
        filtered_data = {}
        for point_name, coords in data.items():
            filtered_data[point_name] = {'x': [], 'y': [], 'z': []}
            
            for axis in ['x', 'y', 'z']:
                values = np.array(coords[axis])
                # Handle NaN values
                valid_mask = ~pd.isna(values)
                if np.sum(valid_mask) < window_size:
                    filtered_data[point_name][axis] = coords[axis]
                    continue
                
                # Apply moving average only to valid data
                valid_values = values[valid_mask]
                filtered_valid = np.convolve(valid_values, np.ones(window_size)/window_size, mode='same')
                
                # Reconstruct full array with NaNs preserved
                filtered_values = np.copy(values)
                filtered_values[valid_mask] = filtered_valid
                filtered_data[point_name][axis] = filtered_values.tolist()
                
        return filtered_data
    
    def apply_butterworth_filter(self, data, cutoff=6, fs=30, order=4):
        """Apply zero-phase Butterworth filter to data"""
        filtered_data = {}
        nyquist = 0.5 * fs
        normal_cutoff = cutoff / nyquist
        b, a = signal.butter(order, normal_cutoff, btype='low', analog=False)
        
        for point_name, coords in data.items():
            filtered_data[point_name] = {'x': [], 'y': [], 'z': []}
            
            for axis in ['x', 'y', 'z']:
                values = np.array(coords[axis])
                # Handle NaN values
                valid_mask = ~pd.isna(values)
                if np.sum(valid_mask) < 2 * order:
                    filtered_data[point_name][axis] = coords[axis]
                    continue
                
                # Apply filter only to valid data
                valid_values = values[valid_mask]
                filtered_valid = signal.filtfilt(b, a, valid_values)
                
                # Reconstruct full array with NaNs preserved
                filtered_values = np.copy(values)
                filtered_values[valid_mask] = filtered_valid
                filtered_data[point_name][axis] = filtered_values.tolist()
                
        return filtered_data
    
    def apply_gaussian_filter(self, data, sigma=2):
        """Apply Gaussian filter to data"""
        filtered_data = {}
        
        for point_name, coords in data.items():
            filtered_data[point_name] = {'x': [], 'y': [], 'z': []}
            
            for axis in ['x', 'y', 'z']:
                values = np.array(coords[axis])
                # Handle NaN values
                valid_mask = ~pd.isna(values)
                if np.sum(valid_mask) < 3:
                    filtered_data[point_name][axis] = coords[axis]
                    continue
                
                # Apply filter only to valid data
                valid_values = values[valid_mask]
                filtered_valid = gaussian_filter1d(valid_values, sigma=sigma)
                
                # Reconstruct full array with NaNs preserved
                filtered_values = np.copy(values)
                filtered_values[valid_mask] = filtered_valid
                filtered_data[point_name][axis] = filtered_values.tolist()
                
        return filtered_data
    
    def setup_layout(self):
        """Setup the Dash app layout"""
        self.app.layout = html.Div([
            html.H1("Kinetisense Data Filter", style={'textAlign': 'center'}),
            
            html.Div([
                html.Label("Kinetisense CSV File Path:"),
                dcc.Input(
                    id='file-path-input',
                    type='text',
                    placeholder='Enter path to Kinetisense CSV file',
                    value='',
                    style={'width': '400px', 'marginRight': '10px'}
                ),
                html.Button('Load Data', id='load-button', n_clicks=0),
                html.Div(id='data-info', style={'marginTop': '10px', 'fontStyle': 'italic'})
            ], style={'margin': '20px'}),
            
            html.Hr(),
            
            html.Div([
                html.H3("Filter Settings", style={'textAlign': 'center'}),
                
                html.Div([
                    html.Label("Select Joint:"),
                    dcc.Dropdown(
                        id='joint-dropdown',
                        options=[],
                        value=None
                    ),
                ], style={'width': '30%', 'display': 'inline-block', 'marginRight': '20px'}),
                
                html.Div([
                    html.Label("Select Axis:"),
                    dcc.RadioItems(
                        id='axis-radio',
                        options=[
                            {'label': 'X', 'value': 'x'},
                            {'label': 'Y', 'value': 'y'},
                            {'label': 'Z', 'value': 'z'}
                        ],
                        value='x',
                        inline=True
                    ),
                ], style={'width': '20%', 'display': 'inline-block', 'marginRight': '20px'}),
                
                html.Div([
                    html.Label("Filters:"),
                    dcc.Checklist(
                        id='filter-checklist',
                        options=[
                            {'label': 'Moving Average', 'value': 'moving_avg'},
                            {'label': 'Butterworth', 'value': 'butterworth'},
                            {'label': 'Gaussian', 'value': 'gaussian'}
                        ],
                        value=[],
                        inline=True
                    ),
                ], style={'width': '40%', 'display': 'inline-block'}),
            ], style={'margin': '20px'}),
            
            html.Div([
                html.Div([
                    html.Label("Moving Average Window Size:"),
                    dcc.Slider(
                        id='ma-window-slider',
                        min=3,
                        max=21,
                        step=2,
                        value=5,
                        marks={i: str(i) for i in range(3, 22, 3)},
                        tooltip={"placement": "bottom", "always_visible": True}
                    ),
                ], style={'width': '30%', 'display': 'inline-block', 'marginRight': '20px'}),
                
                html.Div([
                    html.Label("Butterworth Cutoff Frequency (Hz):"),
                    dcc.Slider(
                        id='butterworth-cutoff-slider',
                        min=1,
                        max=15,
                        step=1,
                        value=6,
                        marks={i: str(i) for i in range(1, 16, 2)},
                        tooltip={"placement": "bottom", "always_visible": True}
                    ),
                ], style={'width': '30%', 'display': 'inline-block', 'marginRight': '20px'}),
                
                html.Div([
                    html.Label("Gaussian Sigma:"),
                    dcc.Slider(
                        id='gaussian-sigma-slider',
                        min=0.5,
                        max=5,
                        step=0.5,
                        value=2,
                        marks={i: str(i) for i in [0.5, 1, 2, 3, 4, 5]},
                        tooltip={"placement": "bottom", "always_visible": True}
                    ),
                ], style={'width': '30%', 'display': 'inline-block'}),
            ], style={'margin': '20px'}),
            
            html.Button('Apply Filters', id='apply-filters-button', n_clicks=0, 
                       style={'margin': '20px', 'padding': '10px', 'backgroundColor': '#4CAF50', 'color': 'white'}),
            
            dcc.Graph(id='data-plot', style={'height': '600px'}),
            
            html.Div([
                html.Button('Export Filtered Data', id='export-button', n_clicks=0,
                           style={'margin': '20px', 'padding': '10px', 'backgroundColor': '#008CBA', 'color': 'white'}),
                html.Div(id='export-status', style={'margin': '20px'})
            ])
        ])
    
    def setup_callbacks(self):
        """Setup Dash callbacks"""
        
        @self.app.callback(
            [Output('data-info', 'children'),
             Output('joint-dropdown', 'options')],
            [Input('load-button', 'n_clicks')],
            [State('file-path-input', 'value')]
        )
        def load_data(n_clicks, file_path):
            if n_clicks > 0 and file_path:
                try:
                    self.load_kinetisense_csv(file_path)
                    info_text = f"Loaded {len(self.point_names)} joints with {len(self.data[self.point_names[0]]['x'])} frames"
                    joint_options = [{'label': name, 'value': name} for name in self.point_names]
                    return info_text, joint_options
                except Exception as e:
                    return f"Error loading file: {str(e)}", []
            return "", []
        
        @self.app.callback(
            Output('data-plot', 'figure'),
            [Input('apply-filters-button', 'n_clicks'),
             Input('joint-dropdown', 'value'),
             Input('axis-radio', 'value')],
            [State('filter-checklist', 'value'),
             State('ma-window-slider', 'value'),
             State('butterworth-cutoff-slider', 'value'),
             State('gaussian-sigma-slider', 'value')]
        )
        def update_plot(n_clicks, joint, axis, filters, ma_window, butter_cutoff, gauss_sigma):
            if not joint or not self.data:
                return {
                    'data': [],
                    'layout': {
                        'title': 'No data loaded or joint selected',
                        'xaxis': {'title': 'Frame'},
                        'yaxis': {'title': 'Position'}
                    }
                }
            
            # Get original data
            original_data = self.data[joint][axis]
            
            # Create figure
            fig = {
                'data': [
                    {'x': list(range(len(original_data))), 
                     'y': original_data, 
                     'type': 'line', 
                     'name': 'Original Data'}
                ],
                'layout': {
                    'title': f'{joint} - {axis.upper()} Axis',
                    'xaxis': {'title': 'Frame'},
                    'yaxis': {'title': f'{axis.upper()} Position'},
                    'legend': {'x': 0, 'y': 1}
                }
            }
            
            # Apply selected filters
            current_data = self.data
            
            if 'moving_avg' in filters:
                filtered_data = self.apply_moving_average(current_data, window_size=ma_window)
                fig['data'].append({
                    'x': list(range(len(filtered_data[joint][axis]))),
                    'y': filtered_data[joint][axis],
                    'type': 'line',
                    'name': f'Moving Average (window={ma_window})'
                })
                current_data = filtered_data
            
            if 'butterworth' in filters:
                filtered_data = self.apply_butterworth_filter(current_data, cutoff=butter_cutoff)
                fig['data'].append({
                    'x': list(range(len(filtered_data[joint][axis]))),
                    'y': filtered_data[joint][axis],
                    'type': 'line',
                    'name': f'Butterworth (cutoff={butter_cutoff}Hz)'
                })
                current_data = filtered_data
            
            if 'gaussian' in filters:
                filtered_data = self.apply_gaussian_filter(current_data, sigma=gauss_sigma)
                fig['data'].append({
                    'x': list(range(len(filtered_data[joint][axis]))),
                    'y': filtered_data[joint][axis],
                    'type': 'line',
                    'name': f'Gaussian (sigma={gauss_sigma})'
                })
                current_data = filtered_data
            
            # Save the final filtered data
            self.filtered_data = current_data
            
            return fig
        
        @self.app.callback(
            Output('export-status', 'children'),
            [Input('export-button', 'n_clicks')],
            [State('file-path-input', 'value')]
        )
        def export_filtered_data(n_clicks, file_path):
            if n_clicks > 0 and self.filtered_data and file_path:
                try:
                    # Create a DataFrame from the filtered data
                    data_rows = []
                    
                    # Determine the number of frames
                    first_joint = next(iter(self.filtered_data))
                    num_frames = len(self.filtered_data[first_joint]['x'])
                    
                    for frame in range(num_frames):
                        row = {}
                        for joint in self.filtered_data:
                            row[f"{joint} X"] = self.filtered_data[joint]['x'][frame]
                            row[f"{joint} Y"] = self.filtered_data[joint]['y'][frame]
                            row[f"{joint} Z"] = self.filtered_data[joint]['z'][frame]
                        data_rows.append(row)
                    
                    df = pd.DataFrame(data_rows)
                    
                    # Save to CSV
                    output_path = file_path.replace('.csv', '_filtered.csv')
                    df.to_csv(output_path, index=False)
                    
                    return f"Filtered data exported to {output_path}"
                except Exception as e:
                    return f"Error exporting data: {str(e)}"
            return "Click 'Export Filtered Data' after applying filters"
    
    def run(self, debug=True, port=8051):
        """Run the Dash app"""
        self.app.run(debug=debug, port=port)

def main():
    app = KinetisenseFilterApp()
    app.run()

if __name__ == "__main__":
    main()