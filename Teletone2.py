    import dash
from dash import dcc, html
from dash.dependencies import Input, Output, State
import plotly.graph_objs as go
from collections import deque
import time
import random
import threading
import serial
from xbee import XBee
import csv
import signal
import sys

class XBeeSensorVisualizer:
    def __init__(self, port="COM7", baudrate=57600, max_points=200, mock=False, runtime_seconds=None):
        # Serial setup
        self.serial_port = port
        self.baudrate = baudrate
        self.serial_conn = None
        self.xbee = None
        self.mock = mock

        # Define units for each sensor (add appropriate units here)
        self.sensor_units = {
            "Pinch Force": "N",
            "Force Pad": "N",
            "Joint Angle": "deg",
            "Joint Angle Vel degree/s": "deg/s",
            "EMG1": "mV",
            "EMG2": "mV",
            "AMG": "units",
            "Force Pad2": "N",  # Added new sensor with unit
        }

        # Data setup
        self.time_data = deque(maxlen=max_points)  # Time axis
        self.streams = {
            "Pinch Force": deque(maxlen=max_points),
            "Force Pad": deque(maxlen=max_points),
            "Joint Angle": deque(maxlen=max_points),
            "Joint Angle Vel degree/s": deque(maxlen=max_points),
            "EMG1": deque(maxlen=max_points),
            "EMG2": deque(maxlen=max_points),
            "AMG": deque(maxlen=max_points),
            "Force Pad2": deque(maxlen=max_points),  # Added new sensor data stream
        }
        self.saved_data = []  # To store all collected data
        self.running = True
        self.data_thread = None
        
        # Set up signal handler for graceful shutdown
        signal.signal(signal.SIGINT, self.signal_handler)

        # Start data collection thread
        if not self.mock:
            self.connect_serial()
            self.data_thread = threading.Thread(target=self.serial_reader_thread, daemon=True)
            self.data_thread.start()
        else:
            self.data_thread = threading.Thread(target=self.mock_reader_thread, daemon=True)
            self.data_thread.start()

        # Optional auto-stop timer
        if runtime_seconds:
            print(f"Auto-stop set for {runtime_seconds} seconds")
            self.stop_timer = threading.Timer(runtime_seconds, self.stop)
            self.stop_timer.daemon = True
            self.stop_timer.start()

        # Initialize Dash app
        self.app = dash.Dash(__name__)
        self.setup_layout()
        self.setup_callbacks()

    def signal_handler(self, sig, frame):
        """Handle keyboard interrupts (Ctrl+C)"""
        print("\nReceived keyboard interrupt, stopping gracefully...")
        self.stop()
        sys.exit(0)

    def connect_serial(self):
        """Initialize serial connection with XBee."""
        try:
            self.serial_conn = serial.Serial(
                baudrate=self.baudrate,
                bytesize=8,
                port=self.serial_port,
                timeout=5
            )
            self.xbee = XBee(self.serial_conn)
            print("Serial connection established.")
        except Exception as e:
            print("Error connecting to serial port:", e)
            print("Falling back to mock data mode.")
            self.mock = True

    def serial_read(self):
        """Reads data from XBee and processes it."""
        try:
            raw_data = self.xbee.wait_read_frame()
            data = raw_data["rf_data"]
            hex_data = data.hex()
            data_bytes = [hex_data[i:i + 2] for i in range(0, len(hex_data), 2)]
            data_decimals = [int(byte, 16) for byte in data_bytes]

            # Combine data into meaningful values
            combined_values = []
            run_time = (data_decimals[2] << 24 |
                        data_decimals[3] << 16 |
                        data_decimals[4] << 8 |
                        data_decimals[5])
            combined_values.append(run_time)

            for i in range(6, len(data_decimals), 2):
                if i + 1 < len(data_decimals):
                    full_value = (data_decimals[i] << 8) | data_decimals[i + 1]
                    combined_values.append(full_value)

            # Process the data
            self.process_data(combined_values)

        except Exception as e:
            if self.running:  # Only print error if we're still supposed to be running
                print("Error reading serial data:", e)

    def mock_serial_read(self):
        """Simulates data for testing without hardware."""
        if not self.running:
            return
            
        runtime = len(self.time_data) + 1 if self.time_data else 0
        data = [runtime] + [random.randint(0, 100) for _ in self.streams.keys()]
        self.process_data(data)
        time.sleep(0.1)  # Simulate delay in data collection

    def process_data(self, data):
        """Process the incoming data and update data structures."""
        if not self.running:
            return
            
        # Save to permanent storage
        self.saved_data.append(data)
        
        # Update live data
        self.time_data.append(data[0])
        for i, key in enumerate(self.streams.keys()):
            if i + 1 < len(data):  # Make sure we have enough data points
                self.streams[key].append(data[i + 1])
            else:
                self.streams[key].append(0)  # Default value if data is missing

    def serial_reader_thread(self):
        """Thread for reading serial data."""
        while self.running:
            try:
                self.serial_read()
            except Exception as e:
                if self.running:  # Only print error if we're still supposed to be running
                    print(f"Error in serial reader thread: {e}")
                    time.sleep(1)  # Prevent error flooding

    def mock_reader_thread(self):
        """Thread for generating mock data."""
        max_runtime = 60000  # Auto-stop after 60 seconds for mock data (optional)
        while self.running:
            try:
                self.mock_serial_read()
                
                # Optional: Check if we should auto-stop based on data runtime
                if self.time_data and self.time_data[-1] > max_runtime and hasattr(self, 'auto_stop_enabled') and self.auto_stop_enabled:
                    print(f"Auto-stopping after reaching {max_runtime}ms of data")
                    self.stop()
                    break
            except Exception as e:
                if self.running:
                    print(f"Error in mock data thread: {e}")
                    time.sleep(1)  # Prevent error flooding

    def save_to_file(self):
        """Saves all collected data to a CSV file."""
        try:
            # Generate a timestamp for the filename
            timestamp = time.strftime("%Y%m%d-%H%M%S")
            filename = f"data_log_{timestamp}.csv"
            
            with open(filename, "w", newline='') as file:
                writer = csv.writer(file)
                # Write header
                header = ["Runtime"] + list(self.streams.keys())
                writer.writerow(header)
                # Write data
                for data in self.saved_data:
                    writer.writerow(data)
            print(f"Data saved to {filename}")
            return filename
        except Exception as e:
            print(f"Error saving data: {e}")
            return None

    def stop(self):
        """Stops the program gracefully."""
        if not self.running:
            print("Already stopped.")
            return
            
        print("Stopping data collection...")
        self.running = False
        
        # Give threads a moment to finish
        time.sleep(0.5)
        
        # Close serial connection if it exists
        if self.serial_conn:
            try:
                self.serial_conn.close()
                print("Serial connection closed.")
            except Exception as e:
                print(f"Error closing serial connection: {e}")
        
        # Save collected data
        filename = self.save_to_file()
        if filename:
            print(f"Program stopped gracefully. Data saved to {filename}")
            return filename
        else:
            print("Program stopped, but data could not be saved.")
            return None

    def setup_layout(self):
        """Set up the Dash app layout."""
        self.app.layout = html.Div([
            html.H1("XBee Sensor Data Visualization", 
                    style={'textAlign': 'center', 'color': '#2c3e50', 'margin-bottom': '30px'}),
            
            # Dashboard information
            html.Div([
                html.H3("Dashboard Information", style={'color': '#2c3e50'}),
                html.Div([
                    html.P(f"Sensor: {sensor} ({unit})", style={'margin': '5px 0'})
                    for sensor, unit in self.sensor_units.items()
                ]),
                html.P("All values displayed as raw unmodified readings from the sensor",
                      style={'fontStyle': 'italic', 'marginTop': '10px'})
            ], style={'marginBottom': '20px', 'backgroundColor': '#f8f9fa', 'padding': '15px', 'borderRadius': '5px'}),
            
            html.Div([
                # First row: 4 plots
                html.Div([
                    html.Div([
                        html.H3(f"Pinch Force ({self.sensor_units['Pinch Force']})", 
                                style={'textAlign': 'center', 'marginBottom': '5px'}),
                        dcc.Graph(id='graph-pinch-force')
                    ], style={'width': '100%', 'padding': '10px'}),
                    
                    html.Div([
                        html.H3(f"Force Pad ({self.sensor_units['Force Pad']})", 
                                style={'textAlign': 'center', 'marginBottom': '5px'}),
                        dcc.Graph(id='graph-force-pad')
                    ], style={'width': '100%', 'padding': '10px'}),
                ], style={'display': 'flex', 'flexWrap': 'wrap'}),
                
                html.Div([
                    html.Div([
                        html.H3(f"Joint Angle ({self.sensor_units['Joint Angle']})", 
                                style={'textAlign': 'center', 'marginBottom': '5px'}),
                        dcc.Graph(id='graph-joint-angle')
                    ], style={'width': '100%', 'padding': '10px'}),
                    
                    html.Div([
                        html.H3(f"Joint Angle Velocity ({self.sensor_units['Joint Angle Vel degree/s']})", 
                                style={'textAlign': 'center', 'marginBottom': '5px'}),
                        dcc.Graph(id='graph-joint-angle-vel-degrees')
                    ], style={'width': '100%', 'padding': '10px'}),
                ], style={'display': 'flex', 'flexWrap': 'wrap'}),
                
                # Second row: 3 plots
                html.Div([
                    html.Div([
                        html.H3(f"EMG1 ({self.sensor_units['EMG1']})", 
                                style={'textAlign': 'center', 'marginBottom': '5px'}),
                        dcc.Graph(id='graph-emg1')
                    ], style={'width': '100%', 'padding': '10px'}),
                    
                    html.Div([
                        html.H3(f"EMG2 ({self.sensor_units['EMG2']})", 
                                style={'textAlign': 'center', 'marginBottom': '5px'}),
                        dcc.Graph(id='graph-emg2')
                    ], style={'width': '100%', 'padding': '10px'}),
                    
                    html.Div([
                        html.H3(f"AMG ({self.sensor_units['AMG']})", 
                                style={'textAlign': 'center', 'marginBottom': '5px'}),
                        dcc.Graph(id='graph-amg')
                    ], style={'width': '100%', 'padding': '10px'}),
                ], style={'display': 'flex', 'flexWrap': 'wrap'}),
                
                # Third row: Force Pad 2 plot
                html.Div([
                    html.Div([
                        html.H3(f"Force Pad2 ({self.sensor_units['Force Pad2']})", 
                                style={'textAlign': 'center', 'marginBottom': '5px'}),
                        dcc.Graph(id='graph-forcepad2')
                    ], style={'width': '100%', 'padding': '10px'}),
                ], style={'display': 'flex', 'flexWrap': 'wrap'}),
            ]),
            
            html.Div([
                html.Button('Stop and Save Data', id='stop-button', 
                            style={'backgroundColor': '#e74c3c', 'color': 'white', 
                                   'padding': '10px 20px', 'margin': '20px', 'border': 'none',
                                   'borderRadius': '5px', 'fontSize': '16px', 'cursor': 'pointer'}),
                html.Div(id='save-status', style={'margin': '20px', 'color': '#27ae60'})
            ], style={'textAlign': 'center'}),
            
            dcc.Interval(
                id='interval-component',
                interval=100,  # in milliseconds
                n_intervals=0
            )
        ], style={'fontFamily': 'Arial', 'margin': '0 auto', 'maxWidth': '1400px'})

    def setup_callbacks(self):
        """Set up the Dash app callbacks."""
        # This is the FIXED version: using a callback factory to properly handle closures
        for sensor_name in self.streams.keys():
            # Convert the sensor name to a valid ID for the graph
            graph_id = f'graph-{sensor_name.lower().replace(" ", "-")}'
            if sensor_name == "Joint Angle Vel degree/s":
                graph_id = "graph-joint-angle-vel-degrees"
                
            # Create a callback for this specific sensor
            self.app.callback(
                Output(graph_id, 'figure'),
                [Input('interval-component', 'n_intervals')]
            )(self.create_callback_function(sensor_name))
        
        # Improved stop button callback
        @self.app.callback(
            Output('save-status', 'children'),
            [Input('stop-button', 'n_clicks')],
            [State('save-status', 'children')]
        )
        def stop_and_save(n_clicks, status):
            # Check if this is an initial call or an actual button click
            if n_clicks is None or n_clicks == 0:
                return status
            
            # Check if we're already stopped
            if not self.running:
                return "Data collection already stopped."
                
            try:
                filename = self.stop()
                if filename:
                    return f"Data collection stopped. Data saved to {filename}"
                else:
                    return "Data collection stopped, but there was an error saving the data."
            except Exception as e:
                return f"Error stopping data collection: {str(e)}"

    def create_callback_function(self, sensor_name):
        """Factory function to create a callback for a specific sensor."""
        def update_graph_for_sensor(n_intervals):
            return self.create_figure(sensor_name)
        return update_graph_for_sensor

    def create_figure(self, sensor):
        """Create a Plotly figure for the given sensor."""
        return {
            'data': [
                {
                    'x': list(self.time_data),
                    'y': list(self.streams[sensor]),
                    'type': 'scatter',
                    'mode': 'lines',
                    'name': sensor,
                    'line': {'color': '#2980b9', 'width': 2}
                }
            ],
            'layout': {
                # Removed title from here as it's now in the H3 element above the graph
                'xaxis': {
                    'title': 'Time (ms)',
                    'tickformat': ',d'  # Display full integers with commas as thousands separators
                },
                'yaxis': {
                    'title': f'Value ({self.sensor_units[sensor]})',
                    'tickformat': ',d'  # Display full integers without abbreviation
                },
                'margin': {'l': 50, 'r': 20, 't': 10, 'b': 50},  # Reduced top margin since title is now above
                'height': 300,
                'paper_bgcolor': 'rgba(0,0,0,0)',
                'plot_bgcolor': 'rgba(240,240,240,0.8)',
                'hovermode': 'closest',
                'uirevision': sensor,  # Keeps zoom level consistent on updates
                'hoverlabel': {
                    'bgcolor': 'white',
                    'font': {'size': 12}
                },
                'hoverformat': '.2f',  # Format for hover text (2 decimal places)
            }
        }

    def start(self):
        """Start the Dash server."""
        print(f"Starting visualization server {'with mock data' if self.mock else ''}")
        print("Press Ctrl+C to stop the server or use the 'Stop and Save Data' button in the UI")
        try:
            self.app.run(debug=False)
        except KeyboardInterrupt:
            print("\nKeyboard interrupt received. Stopping...")
            self.stop()
        except Exception as e:
            print(f"Error in Dash server: {e}")
            self.stop()
        finally:
            # Ensure resources are cleaned up
            if self.running:
                self.stop()


if __name__ == "__main__":
    # Set mock=True to use simulated data, False to use real XBee data
    # Optional: Set runtime_seconds to auto-stop after a specific duration
    visualizer = XBeeSensorVisualizer(mock=False, runtime_seconds=None)
    
    # You can also enable auto-stop after reaching max_runtime in mock mode
    # visualizer.auto_stop_enabled = True
    
    visualizer.start()