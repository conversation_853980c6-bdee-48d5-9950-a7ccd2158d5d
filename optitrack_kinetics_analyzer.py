"""
Sledge Hockey Motion Analysis using Kinetics Toolkit
====================================================

This module provides comprehensive analysis of Optitrack motion capture data
for sledge hockey players using the Kinetics Toolkit (ktk) for biomechanical analysis.

Features:
- Load and process Optitrack CSV files for sledge hockey movements
- Calculate upper body joint angles and velocities
- Perform sledge propulsion analysis
- Analyze stick handling and shooting mechanics
- Calculate sledge speed and acceleration
- Generate biomechanical reports specific to sledge hockey
- Visualize motion data and performance metrics

Requirements:
- kineticstoolkit
- numpy
- pandas
- matplotlib
- plotly
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import kineticstoolkit.lab as ktk
from pathlib import Path
import os
import warnings
warnings.filterwarnings('ignore')


class SledgeHockeyAnalyzer:
    """
    Comprehensive analyzer for sledge hockey motion capture data using Kinetics Toolkit

    Focuses on:
    - Upper body biomechanics (shoulders, elbows, wrists)
    - Sledge propulsion mechanics
    - Stick handling analysis
    - Shooting biomechanics
    - Performance metrics specific to sledge hockey
    """

    def __init__(self):
        self.ts_markers = None  # TimeSeries for marker data
        self.ts_angles = None   # TimeSeries for joint angles
        self.ts_velocities = None  # TimeSeries for velocities
        self.ts_stick = None    # TimeSeries for stick data
        self.marker_names = []
        self.analysis_results = {}
        self.sledge_markers = []  # Markers on the sledge
        self.stick_markers = []   # Markers on the stick
        self.output_dir = None    # Output directory for generated files
        
    def load_optitrack_csv(self, file_path):
        """
        Load Optitrack CSV file and convert to Kinetics Toolkit TimeSeries
        
        Args:
            file_path (str): Path to the Optitrack CSV file
            
        Returns:
            ktk.TimeSeries: Loaded marker data
        """
        print(f"Loading Optitrack file: {file_path}")
        
        # Read the CSV file manually to handle Optitrack format
        with open(file_path, 'r') as file:
            lines = file.readlines()
        
        # Parse Optitrack CSV structure
        if len(lines) < 8:
            raise ValueError("Invalid Optitrack CSV format")
        
        # Extract marker names from row 3
        name_row = [cell.strip() for cell in lines[3].strip().split(',')]
        
        # Extract headers from row 6
        header_row = [cell.strip() for cell in lines[6].strip().split(',')]
        
        # Parse marker names and create mapping
        marker_names = []
        column_mapping = {}
        
        col_idx = 2  # Start after Frame and Time columns
        while col_idx < len(name_row):
            if col_idx < len(name_row) and name_row[col_idx]:
                marker_name = name_row[col_idx]
                if marker_name and marker_name not in marker_names:
                    marker_names.append(marker_name)
                    
                    # Map X, Y, Z columns for this marker
                    if col_idx < len(header_row):
                        column_mapping[f"{marker_name}_x"] = col_idx
                    if col_idx + 1 < len(header_row):
                        column_mapping[f"{marker_name}_y"] = col_idx + 1
                    if col_idx + 2 < len(header_row):
                        column_mapping[f"{marker_name}_z"] = col_idx + 2
            
            col_idx += 3  # Move to next marker
        
        print(f"Found {len(marker_names)} markers: {marker_names}")
        self.marker_names = marker_names

        # Identify sledge hockey specific markers
        self._identify_sledge_hockey_markers(marker_names)
        
        # Extract data starting from row 7
        data_rows = []
        timestamps = []
        
        for i in range(7, len(lines)):
            row = [cell.strip() for cell in lines[i].strip().split(',')]
            if not row or not row[0]:
                continue
                
            try:
                # Extract timestamp
                timestamp = float(row[1]) if len(row) > 1 and row[1] else len(data_rows) / 120.0
                timestamps.append(timestamp)
                
                # Extract marker data
                marker_data = {}
                for marker_col, col_idx in column_mapping.items():
                    if col_idx < len(row) and row[col_idx]:
                        try:
                            value = float(row[col_idx])
                            # Convert zero values to NaN (missing markers)
                            marker_data[marker_col] = value if value != 0.0 else np.nan
                        except (ValueError, TypeError):
                            marker_data[marker_col] = np.nan
                    else:
                        marker_data[marker_col] = np.nan
                
                data_rows.append(marker_data)
                
            except (ValueError, IndexError):
                continue
        
        # Create DataFrame
        df = pd.DataFrame(data_rows)
        
        # Create Kinetics Toolkit TimeSeries
        self.ts_markers = ktk.TimeSeries()
        self.ts_markers.time = np.array(timestamps)
        
        # Add marker data to TimeSeries
        for marker in marker_names:
            x_col = f"{marker}_x"
            y_col = f"{marker}_y"
            z_col = f"{marker}_z"
            
            if x_col in df.columns and y_col in df.columns and z_col in df.columns:
                # Create 4x4 homogeneous transformation matrices for each marker
                marker_data = np.zeros((len(df), 4, 4))
                marker_data[:, 3, 3] = 1.0  # Set homogeneous coordinate
                
                # Set translation components
                marker_data[:, 0, 3] = df[x_col].values / 1000.0  # Convert mm to m
                marker_data[:, 1, 3] = df[y_col].values / 1000.0
                marker_data[:, 2, 3] = df[z_col].values / 1000.0
                
                # Set rotation to identity (markers don't have orientation)
                marker_data[:, 0, 0] = 1.0
                marker_data[:, 1, 1] = 1.0
                marker_data[:, 2, 2] = 1.0
                
                self.ts_markers.data[marker] = marker_data
        
        print(f"Loaded {len(timestamps)} frames at {1/np.mean(np.diff(timestamps)):.1f} Hz")
        return self.ts_markers

    def _identify_sledge_hockey_markers(self, marker_names):
        """
        Identify markers specific to sledge hockey setup

        Args:
            marker_names (list): List of all marker names
        """
        # Common sledge hockey marker patterns
        sledge_patterns = ['SLEDGE', 'SLED', 'FRAME', 'RUNNER', 'BLADE']
        stick_patterns = ['STICK', 'BLADE_TIP', 'HANDLE', 'SHAFT']

        self.sledge_markers = []
        self.stick_markers = []

        for marker in marker_names:
            marker_upper = marker.upper()

            # Check for sledge markers
            for pattern in sledge_patterns:
                if pattern in marker_upper:
                    self.sledge_markers.append(marker)
                    break

            # Check for stick markers
            for pattern in stick_patterns:
                if pattern in marker_upper:
                    self.stick_markers.append(marker)
                    break

        print(f"Identified sledge markers: {self.sledge_markers}")
        print(f"Identified stick markers: {self.stick_markers}")

    def create_output_directory(self, base_name="sledge_hockey_analysis"):
        """
        Create output directory for analysis results

        Args:
            base_name (str): Base name for the output directory

        Returns:
            str: Path to created output directory
        """
        # Create data directory if it doesn't exist
        data_dir = Path("data")
        data_dir.mkdir(exist_ok=True)

        # Create timestamped output directory
        from datetime import datetime
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_dir = data_dir / f"{base_name}_{timestamp}"
        output_dir.mkdir(exist_ok=True)

        self.output_dir = output_dir
        print(f"Created output directory: {output_dir}")
        return str(output_dir)
    
    def calculate_upper_body_angles(self):
        """
        Calculate upper body joint angles relevant to sledge hockey

        Returns:
            ktk.TimeSeries: Upper body joint angles in degrees
        """
        if self.ts_markers is None:
            raise ValueError("No marker data loaded. Call load_optitrack_csv() first.")

        print("Calculating upper body joint angles for sledge hockey...")

        self.ts_angles = ktk.TimeSeries()
        self.ts_angles.time = self.ts_markers.time.copy()

        # Define sledge hockey specific joint angle calculations
        angle_calculations = self._define_sledge_hockey_angles()

        for joint_name, calculation in angle_calculations.items():
            try:
                angles = calculation(self.ts_markers)
                if angles is not None:
                    self.ts_angles.data[joint_name] = angles
                    print(f"  Calculated {joint_name}")
            except Exception as e:
                print(f"  Warning: Could not calculate {joint_name}: {e}")

        return self.ts_angles
    
    def _define_sledge_hockey_angles(self):
        """
        Define joint angle calculations specific to sledge hockey biomechanics

        Returns:
            dict: Dictionary of sledge hockey specific angle calculations
        """
        calculations = {}

        # Shoulder flexion/extension (critical for propulsion)
        def calculate_shoulder_flexion(ts):
            try:
                # Right shoulder: requires shoulder, elbow, and torso markers
                if 'RUSP' in ts.data and 'RHLE' in ts.data and 'SXS' in ts.data:
                    shoulder_pos = ts.data['RUSP'][:, :3, 3]
                    elbow_pos = ts.data['RHLE'][:, :3, 3]
                    torso_pos = ts.data['SXS'][:, :3, 3]  # Sacrum as torso reference

                    # Calculate vectors
                    torso_shoulder = shoulder_pos - torso_pos
                    shoulder_elbow = elbow_pos - shoulder_pos

                    # Calculate flexion angle
                    angles = []
                    for i in range(len(torso_shoulder)):
                        if not (np.isnan(torso_shoulder[i]).any() or np.isnan(shoulder_elbow[i]).any()):
                            dot_product = np.dot(torso_shoulder[i], shoulder_elbow[i])
                            norms = np.linalg.norm(torso_shoulder[i]) * np.linalg.norm(shoulder_elbow[i])
                            if norms > 0:
                                angle = np.arccos(np.clip(dot_product / norms, -1.0, 1.0))
                                angles.append(np.degrees(angle))
                            else:
                                angles.append(np.nan)
                        else:
                            angles.append(np.nan)

                    return np.array(angles)
                return None
            except:
                return None

        # Elbow flexion (important for stick control)
        def calculate_elbow_flexion(ts):
            try:
                if 'RUSP' in ts.data and 'RHLE' in ts.data and 'RWRA' in ts.data:
                    shoulder_pos = ts.data['RUSP'][:, :3, 3]
                    elbow_pos = ts.data['RHLE'][:, :3, 3]
                    wrist_pos = ts.data['RWRA'][:, :3, 3]

                    # Calculate vectors
                    upper_arm = elbow_pos - shoulder_pos
                    forearm = wrist_pos - elbow_pos

                    # Calculate elbow angle
                    angles = []
                    for i in range(len(upper_arm)):
                        if not (np.isnan(upper_arm[i]).any() or np.isnan(forearm[i]).any()):
                            dot_product = np.dot(upper_arm[i], forearm[i])
                            norms = np.linalg.norm(upper_arm[i]) * np.linalg.norm(forearm[i])
                            if norms > 0:
                                angle = np.arccos(np.clip(dot_product / norms, -1.0, 1.0))
                                angles.append(180 - np.degrees(angle))  # Convert to flexion angle
                            else:
                                angles.append(np.nan)
                        else:
                            angles.append(np.nan)

                    return np.array(angles)
                return None
            except:
                return None

        # Trunk rotation (important for power generation)
        def calculate_trunk_rotation(ts):
            try:
                if 'RBAK' in ts.data and 'LBAK' in ts.data and 'SXS' in ts.data:
                    right_back = ts.data['RBAK'][:, :3, 3]
                    left_back = ts.data['LBAK'][:, :3, 3]
                    sacrum = ts.data['SXS'][:, :3, 3]

                    # Calculate shoulder line vector
                    shoulder_line = right_back - left_back

                    # Calculate rotation relative to initial position
                    if len(shoulder_line) > 0:
                        initial_direction = shoulder_line[0]
                        angles = []

                        for i in range(len(shoulder_line)):
                            if not np.isnan(shoulder_line[i]).any():
                                dot_product = np.dot(initial_direction, shoulder_line[i])
                                norms = np.linalg.norm(initial_direction) * np.linalg.norm(shoulder_line[i])
                                if norms > 0:
                                    angle = np.arccos(np.clip(dot_product / norms, -1.0, 1.0))
                                    angles.append(np.degrees(angle))
                                else:
                                    angles.append(np.nan)
                            else:
                                angles.append(np.nan)

                        return np.array(angles)
                return None
            except:
                return None

        calculations['right_shoulder_flexion'] = calculate_shoulder_flexion
        calculations['right_elbow_flexion'] = calculate_elbow_flexion
        calculations['trunk_rotation'] = calculate_trunk_rotation

        return calculations
    
    def calculate_velocities(self):
        """
        Calculate marker velocities
        
        Returns:
            ktk.TimeSeries: Marker velocities
        """
        if self.ts_markers is None:
            raise ValueError("No marker data loaded.")
        
        print("Calculating velocities...")
        
        self.ts_velocities = ktk.TimeSeries()
        self.ts_velocities.time = self.ts_markers.time.copy()
        
        for marker_name in self.marker_names:
            if marker_name in self.ts_markers.data:
                # Extract position data
                positions = self.ts_markers.data[marker_name][:, :3, 3]
                
                # Calculate velocity using finite differences
                dt = np.mean(np.diff(self.ts_markers.time))
                velocities = np.gradient(positions, dt, axis=0)
                
                # Calculate speed (magnitude of velocity)
                speeds = np.linalg.norm(velocities, axis=1)
                
                self.ts_velocities.data[f"{marker_name}_velocity"] = velocities
                self.ts_velocities.data[f"{marker_name}_speed"] = speeds
        
        return self.ts_velocities
    
    def analyze_sledge_propulsion(self):
        """
        Analyze sledge propulsion mechanics specific to sledge hockey

        Returns:
            dict: Sledge propulsion analysis results
        """
        if self.ts_markers is None:
            raise ValueError("No marker data loaded.")

        print("Analyzing sledge propulsion mechanics...")

        propulsion_results = {}

        # Calculate sledge speed and acceleration
        if self.sledge_markers:
            # Use first available sledge marker for speed calculation
            sledge_marker = self.sledge_markers[0]
            if sledge_marker in self.ts_markers.data:
                sledge_pos = self.ts_markers.data[sledge_marker][:, :3, 3]
                valid_indices = ~np.isnan(sledge_pos).any(axis=1)

                if np.sum(valid_indices) > 2:
                    valid_positions = sledge_pos[valid_indices]
                    valid_times = self.ts_markers.time[valid_indices]

                    # Calculate instantaneous speeds
                    speeds = []
                    accelerations = []

                    for i in range(1, len(valid_positions)):
                        dt = valid_times[i] - valid_times[i-1]
                        if dt > 0:
                            distance = np.linalg.norm(valid_positions[i] - valid_positions[i-1])
                            speed = distance / dt
                            speeds.append(speed)

                            # Calculate acceleration
                            if i > 1 and len(speeds) > 1:
                                acceleration = (speeds[-1] - speeds[-2]) / dt
                                accelerations.append(acceleration)

                    if speeds:
                        propulsion_results['max_speed'] = np.max(speeds)
                        propulsion_results['average_speed'] = np.mean(speeds)
                        propulsion_results['speed_variability'] = np.std(speeds)

                    if accelerations:
                        propulsion_results['max_acceleration'] = np.max(accelerations)
                        propulsion_results['average_acceleration'] = np.mean(accelerations)

        # Analyze stroke rate and rhythm
        if 'RWRA' in self.ts_markers.data:  # Right wrist for stroke analysis
            wrist_pos = self.ts_markers.data['RWRA'][:, :3, 3]
            valid_indices = ~np.isnan(wrist_pos).any(axis=1)

            if np.sum(valid_indices) > 10:
                valid_positions = wrist_pos[valid_indices]
                valid_times = self.ts_markers.time[valid_indices]

                # Analyze vertical movement to detect stroke cycles
                vertical_pos = valid_positions[:, 2]  # Z-axis (up/down)

                # Find peaks (top of stroke) and valleys (bottom of stroke)
                from scipy.signal import find_peaks

                # Smooth the signal
                from scipy.ndimage import gaussian_filter1d
                smoothed_vertical = gaussian_filter1d(vertical_pos, sigma=2)

                peaks, _ = find_peaks(smoothed_vertical, height=np.mean(smoothed_vertical))
                valleys, _ = find_peaks(-smoothed_vertical, height=-np.mean(smoothed_vertical))

                if len(peaks) > 1:
                    stroke_intervals = np.diff(valid_times[peaks])
                    propulsion_results['stroke_rate'] = 60 / np.mean(stroke_intervals)  # strokes per minute
                    propulsion_results['stroke_rate_variability'] = np.std(stroke_intervals)

                # Calculate stroke amplitude
                if len(peaks) > 0 and len(valleys) > 0:
                    stroke_amplitudes = []
                    for peak in peaks:
                        # Find nearest valley
                        valley_distances = np.abs(valleys - peak)
                        if len(valley_distances) > 0:
                            nearest_valley = valleys[np.argmin(valley_distances)]
                            amplitude = abs(smoothed_vertical[peak] - smoothed_vertical[nearest_valley])
                            stroke_amplitudes.append(amplitude)

                    if stroke_amplitudes:
                        propulsion_results['average_stroke_amplitude'] = np.mean(stroke_amplitudes)
                        propulsion_results['stroke_amplitude_variability'] = np.std(stroke_amplitudes)

        # Calculate power output estimation (simplified)
        if 'max_speed' in propulsion_results and 'max_acceleration' in propulsion_results:
            # Simplified power estimation based on speed and acceleration
            estimated_mass = 80  # kg (player + sledge, approximate)
            max_power = estimated_mass * propulsion_results['max_acceleration'] * propulsion_results['max_speed']
            propulsion_results['estimated_max_power'] = max_power

        self.analysis_results['propulsion'] = propulsion_results
        return propulsion_results

    def analyze_stick_handling(self):
        """
        Analyze stick handling and shooting mechanics

        Returns:
            dict: Stick handling analysis results
        """
        if self.ts_markers is None:
            raise ValueError("No marker data loaded.")

        print("Analyzing stick handling mechanics...")

        stick_results = {}

        if self.stick_markers:
            # Analyze stick tip movement
            stick_tip_marker = None
            for marker in self.stick_markers:
                if 'TIP' in marker.upper() or 'BLADE' in marker.upper():
                    stick_tip_marker = marker
                    break

            if not stick_tip_marker and self.stick_markers:
                stick_tip_marker = self.stick_markers[0]  # Use first stick marker

            if stick_tip_marker and stick_tip_marker in self.ts_markers.data:
                stick_pos = self.ts_markers.data[stick_tip_marker][:, :3, 3]
                valid_indices = ~np.isnan(stick_pos).any(axis=1)

                if np.sum(valid_indices) > 2:
                    valid_positions = stick_pos[valid_indices]
                    valid_times = self.ts_markers.time[valid_indices]

                    # Calculate stick tip speed
                    stick_speeds = []
                    for i in range(1, len(valid_positions)):
                        dt = valid_times[i] - valid_times[i-1]
                        if dt > 0:
                            distance = np.linalg.norm(valid_positions[i] - valid_positions[i-1])
                            speed = distance / dt
                            stick_speeds.append(speed)

                    if stick_speeds:
                        stick_results['max_stick_speed'] = np.max(stick_speeds)
                        stick_results['average_stick_speed'] = np.mean(stick_speeds)

                        # Detect potential shots (high speed events)
                        shot_threshold = np.mean(stick_speeds) + 2 * np.std(stick_speeds)
                        shot_events = [speed for speed in stick_speeds if speed > shot_threshold]
                        stick_results['shot_count'] = len(shot_events)

                        if shot_events:
                            stick_results['max_shot_speed'] = np.max(shot_events)
                            stick_results['average_shot_speed'] = np.mean(shot_events)

                    # Analyze stick movement patterns
                    # Calculate total path length
                    total_path_length = 0
                    for i in range(1, len(valid_positions)):
                        total_path_length += np.linalg.norm(valid_positions[i] - valid_positions[i-1])

                    stick_results['total_stick_path_length'] = total_path_length

                    # Calculate movement efficiency (straight line distance vs actual path)
                    if len(valid_positions) > 1:
                        straight_line_distance = np.linalg.norm(valid_positions[-1] - valid_positions[0])
                        if total_path_length > 0:
                            stick_results['movement_efficiency'] = straight_line_distance / total_path_length

        # Analyze stick angle relative to ice/sledge
        if len(self.stick_markers) >= 2:
            # Use two stick markers to calculate stick orientation
            marker1 = self.stick_markers[0]
            marker2 = self.stick_markers[1]

            if marker1 in self.ts_markers.data and marker2 in self.ts_markers.data:
                pos1 = self.ts_markers.data[marker1][:, :3, 3]
                pos2 = self.ts_markers.data[marker2][:, :3, 3]

                valid_indices = ~(np.isnan(pos1).any(axis=1) | np.isnan(pos2).any(axis=1))

                if np.sum(valid_indices) > 0:
                    valid_pos1 = pos1[valid_indices]
                    valid_pos2 = pos2[valid_indices]

                    # Calculate stick angles
                    stick_angles = []
                    for i in range(len(valid_pos1)):
                        stick_vector = valid_pos2[i] - valid_pos1[i]
                        # Calculate angle with horizontal plane (ice surface)
                        horizontal_length = np.sqrt(stick_vector[0]**2 + stick_vector[1]**2)
                        if horizontal_length > 0:
                            angle = np.degrees(np.arctan2(stick_vector[2], horizontal_length))
                            stick_angles.append(angle)

                    if stick_angles:
                        stick_results['average_stick_angle'] = np.mean(stick_angles)
                        stick_results['stick_angle_range'] = np.max(stick_angles) - np.min(stick_angles)
                        stick_results['stick_angle_variability'] = np.std(stick_angles)

        self.analysis_results['stick_handling'] = stick_results
        return stick_results

    def analyze_range_of_motion(self):
        """
        Analyze range of motion for joints

        Returns:
            dict: Range of motion analysis results
        """
        if self.ts_angles is None:
            print("Calculating joint angles first...")
            self.calculate_joint_angles()

        print("Analyzing range of motion...")

        rom_results = {}

        for joint_name, angles in self.ts_angles.data.items():
            if isinstance(angles, np.ndarray) and angles.ndim == 1:
                valid_angles = angles[~np.isnan(angles)]
                if len(valid_angles) > 0:
                    rom_results[joint_name] = {
                        'min_angle': np.min(valid_angles),
                        'max_angle': np.max(valid_angles),
                        'range_of_motion': np.max(valid_angles) - np.min(valid_angles),
                        'mean_angle': np.mean(valid_angles),
                        'std_angle': np.std(valid_angles)
                    }

        self.analysis_results['range_of_motion'] = rom_results
        return rom_results

    def detect_sledge_events(self, marker_name='RWRA', threshold_method='velocity'):
        """
        Detect sledge hockey specific events (stroke phases, shots, turns)

        Args:
            marker_name (str): Marker to use for event detection (default: right wrist)
            threshold_method (str): Method for event detection

        Returns:
            dict: Detected sledge hockey events with timestamps
        """
        if self.ts_markers is None:
            raise ValueError("No marker data loaded.")

        print(f"Detecting sledge hockey events using {marker_name} marker...")

        events = {'stroke_starts': [], 'stroke_ends': [], 'shots': [], 'turns': []}

        if marker_name in self.ts_markers.data:
            # Extract position data
            positions = self.ts_markers.data[marker_name][:, :3, 3]
            valid_indices = ~np.isnan(positions).any(axis=1)

            if np.sum(valid_indices) > 10:  # Need sufficient data
                valid_positions = positions[valid_indices]
                valid_times = self.ts_markers.time[valid_indices]

                # Calculate velocities
                velocities = np.gradient(valid_positions, np.mean(np.diff(valid_times)), axis=0)
                speeds = np.linalg.norm(velocities, axis=1)

                # Detect stroke events based on vertical movement
                vertical_pos = valid_positions[:, 2]  # Z-axis
                vertical_vel = velocities[:, 2]

                # Find stroke starts (rapid downward movement)
                for i in range(1, len(vertical_vel) - 1):
                    if (vertical_vel[i] < -0.5 and  # Moving down fast
                        vertical_vel[i-1] > vertical_vel[i] and  # Accelerating downward
                        vertical_vel[i+1] < vertical_vel[i]):  # Still accelerating
                        events['stroke_starts'].append(valid_times[i])

                # Find stroke ends (rapid upward movement)
                for i in range(1, len(vertical_vel) - 1):
                    if (vertical_vel[i] > 0.5 and  # Moving up fast
                        vertical_vel[i-1] < vertical_vel[i] and  # Accelerating upward
                        vertical_vel[i+1] > vertical_vel[i]):  # Still accelerating
                        events['stroke_ends'].append(valid_times[i])

                # Detect potential shots (very high speed events)
                speed_threshold = np.mean(speeds) + 3 * np.std(speeds)
                for i, speed in enumerate(speeds):
                    if speed > speed_threshold:
                        events['shots'].append(valid_times[i])

                # Detect turns (changes in horizontal direction)
                horizontal_vel = velocities[:, :2]  # X-Y plane
                horizontal_speeds = np.linalg.norm(horizontal_vel, axis=1)

                # Calculate direction changes
                for i in range(2, len(horizontal_vel) - 2):
                    if horizontal_speeds[i] > 0.1:  # Only when moving
                        # Calculate angle between consecutive velocity vectors
                        v1 = horizontal_vel[i-1]
                        v2 = horizontal_vel[i+1]

                        if np.linalg.norm(v1) > 0 and np.linalg.norm(v2) > 0:
                            cos_angle = np.dot(v1, v2) / (np.linalg.norm(v1) * np.linalg.norm(v2))
                            angle_change = np.degrees(np.arccos(np.clip(cos_angle, -1, 1)))

                            # Detect significant direction changes (turns)
                            if angle_change > 45:  # More than 45 degree change
                                events['turns'].append(valid_times[i])

        self.analysis_results['events'] = events
        return events

    def generate_report(self):
        """
        Generate a comprehensive analysis report

        Returns:
            dict: Complete analysis report
        """
        print("Generating comprehensive analysis report...")

        report = {
            'file_info': {
                'markers_count': len(self.marker_names),
                'marker_names': self.marker_names,
                'duration': self.ts_markers.time[-1] - self.ts_markers.time[0] if self.ts_markers else 0,
                'sampling_rate': 1 / np.mean(np.diff(self.ts_markers.time)) if self.ts_markers else 0
            }
        }

        # Add all analysis results
        if hasattr(self, 'analysis_results'):
            report.update(self.analysis_results)

        # Calculate additional metrics
        if self.ts_markers:
            # Data quality metrics
            quality_metrics = {}
            for marker in self.marker_names:
                if marker in self.ts_markers.data:
                    positions = self.ts_markers.data[marker][:, :3, 3]
                    valid_data = ~np.isnan(positions).any(axis=1)
                    quality_metrics[marker] = {
                        'data_completeness': np.sum(valid_data) / len(valid_data) * 100,
                        'missing_frames': len(valid_data) - np.sum(valid_data)
                    }

            report['data_quality'] = quality_metrics

        return report

    def visualize_markers_3d(self, frame_range=None, show_trails=True):
        """
        Create 3D visualization of marker positions

        Args:
            frame_range (tuple): (start_frame, end_frame) or None for all frames
            show_trails (bool): Whether to show marker trails

        Returns:
            plotly.graph_objects.Figure: 3D plot
        """
        if self.ts_markers is None:
            raise ValueError("No marker data loaded.")

        fig = go.Figure()

        # Determine frame range
        if frame_range is None:
            start_frame, end_frame = 0, len(self.ts_markers.time)
        else:
            start_frame, end_frame = frame_range

        colors = px.colors.qualitative.Set1

        for i, marker in enumerate(self.marker_names):
            if marker in self.ts_markers.data:
                positions = self.ts_markers.data[marker][start_frame:end_frame, :3, 3]
                valid_indices = ~np.isnan(positions).any(axis=1)

                if np.sum(valid_indices) > 0:
                    valid_positions = positions[valid_indices]
                    color = colors[i % len(colors)]

                    if show_trails and len(valid_positions) > 1:
                        # Add trail
                        fig.add_trace(go.Scatter3d(
                            x=valid_positions[:, 0],
                            y=valid_positions[:, 1],
                            z=valid_positions[:, 2],
                            mode='lines',
                            line=dict(color=color, width=2),
                            name=f'{marker} Trail',
                            opacity=0.6
                        ))

                    # Add current position
                    fig.add_trace(go.Scatter3d(
                        x=[valid_positions[-1, 0]],
                        y=[valid_positions[-1, 1]],
                        z=[valid_positions[-1, 2]],
                        mode='markers',
                        marker=dict(size=8, color=color),
                        name=marker
                    ))

        fig.update_layout(
            title='3D Marker Positions',
            scene=dict(
                xaxis_title='X (m)',
                yaxis_title='Y (m)',
                zaxis_title='Z (m)',
                aspectmode='cube'
            )
        )

        return fig

    def plot_joint_angles(self):
        """
        Plot joint angles over time

        Returns:
            plotly.graph_objects.Figure: Joint angle plots
        """
        if self.ts_angles is None:
            self.calculate_joint_angles()

        if not self.ts_angles.data:
            print("No joint angles calculated.")
            return None

        fig = make_subplots(
            rows=len(self.ts_angles.data),
            cols=1,
            subplot_titles=list(self.ts_angles.data.keys()),
            vertical_spacing=0.1
        )

        for i, (joint_name, angles) in enumerate(self.ts_angles.data.items(), 1):
            if isinstance(angles, np.ndarray) and angles.ndim == 1:
                fig.add_trace(
                    go.Scatter(
                        x=self.ts_angles.time,
                        y=angles,
                        mode='lines',
                        name=joint_name,
                        line=dict(width=2)
                    ),
                    row=i, col=1
                )

        fig.update_layout(
            title='Joint Angles Over Time',
            height=300 * len(self.ts_angles.data),
            showlegend=False
        )

        fig.update_xaxes(title_text='Time (s)')
        fig.update_yaxes(title_text='Angle (degrees)')

        return fig

    def visualize_with_ktk(self, frame_range=None, show_skeleton=True, show_coordinate_systems=True):
        """
        Create 3D visualization using Kinetics Toolkit's built-in visualization

        Args:
            frame_range (tuple): (start_frame, end_frame) or None for all frames
            show_skeleton (bool): Whether to show skeleton connections
            show_coordinate_systems (bool): Whether to show coordinate systems for each marker

        Returns:
            str: Path to saved HTML file
        """
        if self.ts_markers is None:
            raise ValueError("No marker data loaded.")

        print("Creating KTK-based 3D visualization...")

        # Determine frame range
        if frame_range is None:
            start_frame, end_frame = 0, len(self.ts_markers.time)
        else:
            start_frame, end_frame = frame_range

        # Create a subset of the TimeSeries for visualization
        ts_subset = ktk.TimeSeries()
        ts_subset.time = self.ts_markers.time[start_frame:end_frame]

        # Copy marker data for the specified range
        for marker_name in self.marker_names:
            if marker_name in self.ts_markers.data:
                ts_subset.data[marker_name] = self.ts_markers.data[marker_name][start_frame:end_frame]

        # Use KTK's built-in 3D player
        try:
            # Create the 3D player
            player = ktk.Player(ts_subset)

            # Configure visualization options
            if show_coordinate_systems:
                # Show coordinate systems for key markers
                key_markers = ['RWRA', 'LWRA', 'SXS'] + self.sledge_markers[:2] + self.stick_markers[:1]
                available_key_markers = [m for m in key_markers if m in self.marker_names]

                for marker in available_key_markers:
                    player.set_frame_rate(30)  # Set frame rate for smooth playback

            # Save the visualization
            if self.output_dir:
                output_path = Path(self.output_dir) / "ktk_3d_visualization.html"
            else:
                output_path = "ktk_3d_visualization.html"

            # Export to HTML
            player.to_html(str(output_path))
            print(f"  Saved KTK 3D visualization to: {output_path}")
            return str(output_path)

        except Exception as e:
            print(f"  Warning: Could not create KTK visualization: {e}")
            print("  Falling back to custom 3D visualization...")
            return self._create_custom_3d_visualization(frame_range, show_skeleton)

    def _create_custom_3d_visualization(self, frame_range=None, show_skeleton=True):
        """
        Create custom 3D visualization as fallback

        Args:
            frame_range (tuple): (start_frame, end_frame) or None for all frames
            show_skeleton (bool): Whether to show skeleton connections

        Returns:
            str: Path to saved HTML file
        """
        # Determine frame range
        if frame_range is None:
            start_frame, end_frame = 0, len(self.ts_markers.time)
        else:
            start_frame, end_frame = frame_range

        fig = go.Figure()

        colors = px.colors.qualitative.Set1

        # Add marker trajectories
        for i, marker in enumerate(self.marker_names):
            if marker in self.ts_markers.data:
                positions = self.ts_markers.data[marker][start_frame:end_frame, :3, 3]
                valid_indices = ~np.isnan(positions).any(axis=1)

                if np.sum(valid_indices) > 0:
                    valid_positions = positions[valid_indices]
                    color = colors[i % len(colors)]

                    # Add trajectory line
                    fig.add_trace(go.Scatter3d(
                        x=valid_positions[:, 0],
                        y=valid_positions[:, 1],
                        z=valid_positions[:, 2],
                        mode='lines+markers',
                        line=dict(color=color, width=3),
                        marker=dict(size=3, color=color),
                        name=f'{marker} Trajectory',
                        opacity=0.7
                    ))

                    # Highlight start and end points
                    fig.add_trace(go.Scatter3d(
                        x=[valid_positions[0, 0]],
                        y=[valid_positions[0, 1]],
                        z=[valid_positions[0, 2]],
                        mode='markers',
                        marker=dict(size=10, color='green', symbol='circle'),
                        name=f'{marker} Start',
                        showlegend=False
                    ))

                    fig.add_trace(go.Scatter3d(
                        x=[valid_positions[-1, 0]],
                        y=[valid_positions[-1, 1]],
                        z=[valid_positions[-1, 2]],
                        mode='markers',
                        marker=dict(size=10, color='red', symbol='square'),
                        name=f'{marker} End',
                        showlegend=False
                    ))

        # Add skeleton connections if requested
        if show_skeleton and hasattr(self, 'ts_angles'):
            # Add simple skeleton connections between key markers
            skeleton_pairs = [
                ('RUSP', 'RHLE'),  # Right shoulder to elbow
                ('RHLE', 'RWRA'),  # Right elbow to wrist
                ('LUSP', 'LHLE'),  # Left shoulder to elbow
                ('LHLE', 'LWRA'),  # Left elbow to wrist
                ('RUSP', 'LUSP'),  # Shoulder line
                ('SXS', 'RUSP'),   # Torso to right shoulder
                ('SXS', 'LUSP'),   # Torso to left shoulder
            ]

            for marker1, marker2 in skeleton_pairs:
                if (marker1 in self.ts_markers.data and marker2 in self.ts_markers.data):
                    pos1 = self.ts_markers.data[marker1][start_frame:end_frame, :3, 3]
                    pos2 = self.ts_markers.data[marker2][start_frame:end_frame, :3, 3]

                    # Use middle frame for skeleton visualization
                    mid_frame = len(pos1) // 2
                    if (mid_frame < len(pos1) and
                        not np.isnan(pos1[mid_frame]).any() and
                        not np.isnan(pos2[mid_frame]).any()):

                        fig.add_trace(go.Scatter3d(
                            x=[pos1[mid_frame, 0], pos2[mid_frame, 0]],
                            y=[pos1[mid_frame, 1], pos2[mid_frame, 1]],
                            z=[pos1[mid_frame, 2], pos2[mid_frame, 2]],
                            mode='lines',
                            line=dict(color='gray', width=5),
                            name='Skeleton',
                            showlegend=False,
                            opacity=0.6
                        ))

        fig.update_layout(
            title='Sledge Hockey 3D Motion Analysis - Full Trajectory',
            scene=dict(
                xaxis_title='X (m)',
                yaxis_title='Y (m)',
                zaxis_title='Z (m)',
                aspectmode='cube',
                camera=dict(
                    eye=dict(x=1.5, y=1.5, z=1.5)
                )
            ),
            showlegend=True,
            legend=dict(
                yanchor="top",
                y=0.99,
                xanchor="left",
                x=0.01
            )
        )

        # Save the visualization
        if self.output_dir:
            output_path = Path(self.output_dir) / "custom_3d_visualization.html"
        else:
            output_path = "custom_3d_visualization.html"

        fig.write_html(str(output_path))
        print(f"  Saved custom 3D visualization to: {output_path}")
        return str(output_path)

    def plot_velocities(self, marker_names=None):
        """
        Plot marker velocities

        Args:
            marker_names (list): List of markers to plot, or None for all

        Returns:
            plotly.graph_objects.Figure: Velocity plots
        """
        if self.ts_velocities is None:
            self.calculate_velocities()

        if marker_names is None:
            marker_names = self.marker_names[:5]  # Limit to first 5 markers

        fig = go.Figure()

        colors = px.colors.qualitative.Set1

        for i, marker in enumerate(marker_names):
            speed_key = f"{marker}_speed"
            if speed_key in self.ts_velocities.data:
                speeds = self.ts_velocities.data[speed_key]
                fig.add_trace(go.Scatter(
                    x=self.ts_velocities.time,
                    y=speeds,
                    mode='lines',
                    name=f'{marker} Speed',
                    line=dict(color=colors[i % len(colors)], width=2)
                ))

        fig.update_layout(
            title='Marker Speeds Over Time',
            xaxis_title='Time (s)',
            yaxis_title='Speed (m/s)',
            hovermode='x unified'
        )

        return fig


def main():
    """
    Example usage of the SledgeHockeyAnalyzer
    """
    # Initialize analyzer
    analyzer = SledgeHockeyAnalyzer()

    # Example file path - update this to your actual file #TODO Come here to add stuff
    file_path = "data\Interp_Filtered_Take 2025-06-27 08.43.45 AM S2T3 INTP.csv"

    try:
        print("=== Sledge Hockey Motion Analysis using Kinetics Toolkit ===\n")

        # Create output directory
        print("1. Creating output directory...")
        output_dir = analyzer.create_output_directory()

        # Load data
        print("\n2. Loading Optitrack data...")
        analyzer.load_optitrack_csv(file_path)

        # Calculate upper body joint angles
        print("\n3. Calculating upper body joint angles...")
        analyzer.calculate_upper_body_angles()

        # Calculate velocities
        print("\n4. Calculating marker velocities...")
        analyzer.calculate_velocities()

        # Perform sledge propulsion analysis
        print("\n5. Analyzing sledge propulsion mechanics...")
        analyzer.analyze_sledge_propulsion()

        # Analyze stick handling
        print("\n6. Analyzing stick handling...")
        analyzer.analyze_stick_handling()

        # Analyze range of motion
        print("\n7. Analyzing range of motion...")
        analyzer.analyze_range_of_motion()

        # Detect sledge hockey events
        print("\n8. Detecting sledge hockey events...")
        analyzer.detect_sledge_events()

        # Generate comprehensive report
        print("\n9. Generating analysis report...")
        report = analyzer.generate_report()

        # Print summary results
        print("\n=== SLEDGE HOCKEY ANALYSIS RESULTS ===")
        print(f"File Duration: {report['file_info']['duration']:.2f} seconds")
        print(f"Sampling Rate: {report['file_info']['sampling_rate']:.1f} Hz")
        print(f"Number of Markers: {report['file_info']['markers_count']}")
        print(f"Sledge Markers: {analyzer.sledge_markers}")
        print(f"Stick Markers: {analyzer.stick_markers}")

        if 'propulsion' in report:
            print(f"\nSledge Propulsion Analysis:")
            for key, value in report['propulsion'].items():
                if 'speed' in key.lower():
                    print(f"  {key}: {value:.3f} m/s")
                elif 'acceleration' in key.lower():
                    print(f"  {key}: {value:.3f} m/s²")
                elif 'power' in key.lower():
                    print(f"  {key}: {value:.1f} W")
                elif 'rate' in key.lower():
                    print(f"  {key}: {value:.1f} strokes/min")
                else:
                    print(f"  {key}: {value:.4f}")

        if 'stick_handling' in report:
            print(f"\nStick Handling Analysis:")
            for key, value in report['stick_handling'].items():
                if 'speed' in key.lower():
                    print(f"  {key}: {value:.3f} m/s")
                elif 'angle' in key.lower():
                    print(f"  {key}: {value:.2f}°")
                elif 'count' in key.lower():
                    print(f"  {key}: {int(value)}")
                else:
                    print(f"  {key}: {value:.4f}")

        if 'range_of_motion' in report:
            print(f"\nUpper Body Range of Motion:")
            for joint, rom_data in report['range_of_motion'].items():
                print(f"  {joint}: {rom_data['range_of_motion']:.2f}° (min: {rom_data['min_angle']:.2f}°, max: {rom_data['max_angle']:.2f}°)")

        if 'events' in report:
            print(f"\nDetected Sledge Hockey Events:")
            print(f"  Stroke Starts: {len(report['events']['stroke_starts'])}")
            print(f"  Stroke Ends: {len(report['events']['stroke_ends'])}")
            print(f"  Shots: {len(report['events']['shots'])}")
            print(f"  Turns: {len(report['events']['turns'])}")

        # Data quality summary
        if 'data_quality' in report:
            print(f"\nData Quality:")
            avg_completeness = np.mean([metrics['data_completeness'] for metrics in report['data_quality'].values()])
            print(f"  Average Data Completeness: {avg_completeness:.1f}%")

        # Create visualizations
        print("\n10. Creating sledge hockey visualizations...")

        # KTK-based 3D visualization (interactive with timeline)
        print("  Creating KTK 3D interactive visualization...")
        ktk_viz_path = analyzer.visualize_with_ktk()

        # Standard 3D marker visualization
        print("  Creating standard 3D motion visualization...")
        fig_3d = analyzer.visualize_markers_3d()
        fig_3d.update_layout(title="Sledge Hockey 3D Motion Analysis")
        output_path_3d = Path(analyzer.output_dir) / "sledge_hockey_3d_motion.html"
        fig_3d.write_html(str(output_path_3d))
        print(f"  Saved 3D motion visualization to: {output_path_3d}")

        # Upper body joint angle plots
        if analyzer.ts_angles and analyzer.ts_angles.data:
            print("  Creating joint angle plots...")
            fig_angles = analyzer.plot_joint_angles()
            if fig_angles:
                fig_angles.update_layout(title="Sledge Hockey Upper Body Joint Angles")
                output_path_angles = Path(analyzer.output_dir) / "sledge_hockey_joint_angles.html"
                fig_angles.write_html(str(output_path_angles))
                print(f"  Saved joint angle plots to: {output_path_angles}")

        # Velocity plots (focus on key markers)
        print("  Creating velocity analysis plots...")
        key_markers = ['RWRA', 'LWRA'] + analyzer.sledge_markers[:2] + analyzer.stick_markers[:1]
        available_markers = [m for m in key_markers if m in analyzer.marker_names]

        if available_markers:
            fig_velocities = analyzer.plot_velocities(available_markers)
            fig_velocities.update_layout(title="Sledge Hockey Key Marker Velocities")
            output_path_velocities = Path(analyzer.output_dir) / "sledge_hockey_velocities.html"
            fig_velocities.write_html(str(output_path_velocities))
            print(f"  Saved velocity plots to: {output_path_velocities}")

        # Save detailed report to JSON
        print("  Saving comprehensive analysis report...")
        import json

        # Convert numpy arrays to lists for JSON serialization
        def convert_numpy(obj):
            if isinstance(obj, np.ndarray):
                return obj.tolist()
            elif isinstance(obj, np.integer):
                return int(obj)
            elif isinstance(obj, np.floating):
                return float(obj)
            elif isinstance(obj, dict):
                return {key: convert_numpy(value) for key, value in obj.items()}
            elif isinstance(obj, list):
                return [convert_numpy(item) for item in obj]
            else:
                return obj

        json_report = convert_numpy(report)

        output_path_json = Path(analyzer.output_dir) / "sledge_hockey_analysis_report.json"
        with open(output_path_json, "w") as f:
            json.dump(json_report, f, indent=2)
        print(f"  Saved detailed report to: {output_path_json}")

        # Create a summary text file
        output_path_summary = Path(analyzer.output_dir) / "analysis_summary.txt"
        with open(output_path_summary, "w") as f:
            f.write("=== SLEDGE HOCKEY ANALYSIS SUMMARY ===\n\n")
            f.write(f"File Duration: {report['file_info']['duration']:.2f} seconds\n")
            f.write(f"Sampling Rate: {report['file_info']['sampling_rate']:.1f} Hz\n")
            f.write(f"Number of Markers: {report['file_info']['markers_count']}\n")
            f.write(f"Sledge Markers: {analyzer.sledge_markers}\n")
            f.write(f"Stick Markers: {analyzer.stick_markers}\n\n")

            if 'propulsion' in report:
                f.write("SLEDGE PROPULSION ANALYSIS:\n")
                for key, value in report['propulsion'].items():
                    if 'speed' in key.lower():
                        f.write(f"  {key}: {value:.3f} m/s\n")
                    elif 'acceleration' in key.lower():
                        f.write(f"  {key}: {value:.3f} m/s²\n")
                    elif 'power' in key.lower():
                        f.write(f"  {key}: {value:.1f} W\n")
                    elif 'rate' in key.lower():
                        f.write(f"  {key}: {value:.1f} strokes/min\n")
                    else:
                        f.write(f"  {key}: {value:.4f}\n")
                f.write("\n")

            if 'stick_handling' in report:
                f.write("STICK HANDLING ANALYSIS:\n")
                for key, value in report['stick_handling'].items():
                    if 'speed' in key.lower():
                        f.write(f"  {key}: {value:.3f} m/s\n")
                    elif 'angle' in key.lower():
                        f.write(f"  {key}: {value:.2f}°\n")
                    elif 'count' in key.lower():
                        f.write(f"  {key}: {int(value)}\n")
                    else:
                        f.write(f"  {key}: {value:.4f}\n")
                f.write("\n")

            if 'events' in report:
                f.write("DETECTED EVENTS:\n")
                f.write(f"  Stroke Starts: {len(report['events']['stroke_starts'])}\n")
                f.write(f"  Stroke Ends: {len(report['events']['stroke_ends'])}\n")
                f.write(f"  Shots: {len(report['events']['shots'])}\n")
                f.write(f"  Turns: {len(report['events']['turns'])}\n")

        print(f"  Saved analysis summary to: {output_path_summary}")

        print(f"\n=== Sledge Hockey Analysis Complete! ===")
        print(f"All files saved to: {analyzer.output_dir}")
        print("\nGenerated files:")
        print("  - ktk_3d_visualization.html (KTK interactive 3D with timeline)")
        print("  - custom_3d_visualization.html (Custom 3D trajectory view)")
        print("  - sledge_hockey_3d_motion.html (Standard 3D visualization)")
        print("  - sledge_hockey_joint_angles.html (Upper body biomechanics)")
        print("  - sledge_hockey_velocities.html (Speed analysis)")
        print("  - sledge_hockey_analysis_report.json (Detailed data)")
        print("  - analysis_summary.txt (Human-readable summary)")
        print(f"\nOpen the HTML files in your browser for interactive analysis!")
        print(f"Full path: {Path(analyzer.output_dir).absolute()}")

    except FileNotFoundError:
        print(f"Error: Could not find file '{file_path}'")
        print("Please update the file_path variable with the correct path to your Optitrack CSV file.")
    except Exception as e:
        print(f"Error during analysis: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
