"""
Optitrack Motion Analysis using Kinetics Toolkit
================================================

This module provides comprehensive analysis of Optitrack motion capture data
using the Kinetics Toolkit (ktk) for biomechanical analysis.

Features:
- Load and process Optitrack CSV files
- Calculate joint angles and velocities
- Perform gait analysis
- Generate biomechanical reports
- Visualize motion data and analysis results

Requirements:
- kineticstoolkit
- numpy
- pandas
- matplotlib
- plotly
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import kineticstoolkit.lab as ktk
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')


class OptitrackKineticsAnalyzer:
    """
    Comprehensive analyzer for Optitrack motion capture data using Kinetics Toolkit
    """
    
    def __init__(self):
        self.ts_markers = None  # TimeSeries for marker data
        self.ts_angles = None   # TimeSeries for joint angles
        self.ts_velocities = None  # TimeSeries for velocities
        self.marker_names = []
        self.analysis_results = {}
        
    def load_optitrack_csv(self, file_path):
        """
        Load Optitrack CSV file and convert to Kinetics Toolkit TimeSeries
        
        Args:
            file_path (str): Path to the Optitrack CSV file
            
        Returns:
            ktk.TimeSeries: Loaded marker data
        """
        print(f"Loading Optitrack file: {file_path}")
        
        # Read the CSV file manually to handle Optitrack format
        with open(file_path, 'r') as file:
            lines = file.readlines()
        
        # Parse Optitrack CSV structure
        if len(lines) < 8:
            raise ValueError("Invalid Optitrack CSV format")
        
        # Extract marker names from row 3
        name_row = [cell.strip() for cell in lines[3].strip().split(',')]
        
        # Extract headers from row 6
        header_row = [cell.strip() for cell in lines[6].strip().split(',')]
        
        # Parse marker names and create mapping
        marker_names = []
        column_mapping = {}
        
        col_idx = 2  # Start after Frame and Time columns
        while col_idx < len(name_row):
            if col_idx < len(name_row) and name_row[col_idx]:
                marker_name = name_row[col_idx]
                if marker_name and marker_name not in marker_names:
                    marker_names.append(marker_name)
                    
                    # Map X, Y, Z columns for this marker
                    if col_idx < len(header_row):
                        column_mapping[f"{marker_name}_x"] = col_idx
                    if col_idx + 1 < len(header_row):
                        column_mapping[f"{marker_name}_y"] = col_idx + 1
                    if col_idx + 2 < len(header_row):
                        column_mapping[f"{marker_name}_z"] = col_idx + 2
            
            col_idx += 3  # Move to next marker
        
        print(f"Found {len(marker_names)} markers: {marker_names}")
        self.marker_names = marker_names
        
        # Extract data starting from row 7
        data_rows = []
        timestamps = []
        
        for i in range(7, len(lines)):
            row = [cell.strip() for cell in lines[i].strip().split(',')]
            if not row or not row[0]:
                continue
                
            try:
                # Extract timestamp
                timestamp = float(row[1]) if len(row) > 1 and row[1] else len(data_rows) / 120.0
                timestamps.append(timestamp)
                
                # Extract marker data
                marker_data = {}
                for marker_col, col_idx in column_mapping.items():
                    if col_idx < len(row) and row[col_idx]:
                        try:
                            value = float(row[col_idx])
                            # Convert zero values to NaN (missing markers)
                            marker_data[marker_col] = value if value != 0.0 else np.nan
                        except (ValueError, TypeError):
                            marker_data[marker_col] = np.nan
                    else:
                        marker_data[marker_col] = np.nan
                
                data_rows.append(marker_data)
                
            except (ValueError, IndexError):
                continue
        
        # Create DataFrame
        df = pd.DataFrame(data_rows)
        
        # Create Kinetics Toolkit TimeSeries
        self.ts_markers = ktk.TimeSeries()
        self.ts_markers.time = np.array(timestamps)
        
        # Add marker data to TimeSeries
        for marker in marker_names:
            x_col = f"{marker}_x"
            y_col = f"{marker}_y"
            z_col = f"{marker}_z"
            
            if x_col in df.columns and y_col in df.columns and z_col in df.columns:
                # Create 4x4 homogeneous transformation matrices for each marker
                marker_data = np.zeros((len(df), 4, 4))
                marker_data[:, 3, 3] = 1.0  # Set homogeneous coordinate
                
                # Set translation components
                marker_data[:, 0, 3] = df[x_col].values / 1000.0  # Convert mm to m
                marker_data[:, 1, 3] = df[y_col].values / 1000.0
                marker_data[:, 2, 3] = df[z_col].values / 1000.0
                
                # Set rotation to identity (markers don't have orientation)
                marker_data[:, 0, 0] = 1.0
                marker_data[:, 1, 1] = 1.0
                marker_data[:, 2, 2] = 1.0
                
                self.ts_markers.data[marker] = marker_data
        
        print(f"Loaded {len(timestamps)} frames at {1/np.mean(np.diff(timestamps)):.1f} Hz")
        return self.ts_markers
    
    def calculate_joint_angles(self):
        """
        Calculate joint angles from marker positions
        
        Returns:
            ktk.TimeSeries: Joint angles in degrees
        """
        if self.ts_markers is None:
            raise ValueError("No marker data loaded. Call load_optitrack_csv() first.")
        
        print("Calculating joint angles...")
        
        self.ts_angles = ktk.TimeSeries()
        self.ts_angles.time = self.ts_markers.time.copy()
        
        # Define joint angle calculations based on available markers
        angle_calculations = self._define_joint_angle_calculations()
        
        for joint_name, calculation in angle_calculations.items():
            try:
                angles = calculation(self.ts_markers)
                if angles is not None:
                    self.ts_angles.data[joint_name] = angles
                    print(f"  Calculated {joint_name}")
            except Exception as e:
                print(f"  Warning: Could not calculate {joint_name}: {e}")
        
        return self.ts_angles
    
    def _define_joint_angle_calculations(self):
        """
        Define joint angle calculation functions based on marker availability
        
        Returns:
            dict: Dictionary of joint angle calculation functions
        """
        calculations = {}
        
        # Example: Knee flexion angle (requires thigh, knee, and ankle markers)
        def calculate_knee_flexion(ts):
            try:
                # This is a simplified example - real biomechanical calculations are more complex
                if 'RHGT' in ts.data and 'RKNE' in ts.data and 'RCAJ' in ts.data:
                    # Extract positions
                    hip_pos = ts.data['RHGT'][:, :3, 3]
                    knee_pos = ts.data['RKNE'][:, :3, 3]
                    ankle_pos = ts.data['RCAJ'][:, :3, 3]
                    
                    # Calculate vectors
                    thigh_vector = knee_pos - hip_pos
                    shank_vector = ankle_pos - knee_pos
                    
                    # Calculate angle between vectors
                    angles = []
                    for i in range(len(thigh_vector)):
                        if not (np.isnan(thigh_vector[i]).any() or np.isnan(shank_vector[i]).any()):
                            dot_product = np.dot(thigh_vector[i], shank_vector[i])
                            norms = np.linalg.norm(thigh_vector[i]) * np.linalg.norm(shank_vector[i])
                            if norms > 0:
                                angle = np.arccos(np.clip(dot_product / norms, -1.0, 1.0))
                                angles.append(np.degrees(angle))
                            else:
                                angles.append(np.nan)
                        else:
                            angles.append(np.nan)
                    
                    return np.array(angles)
                return None
            except:
                return None
        
        calculations['right_knee_flexion'] = calculate_knee_flexion
        
        return calculations
    
    def calculate_velocities(self):
        """
        Calculate marker velocities
        
        Returns:
            ktk.TimeSeries: Marker velocities
        """
        if self.ts_markers is None:
            raise ValueError("No marker data loaded.")
        
        print("Calculating velocities...")
        
        self.ts_velocities = ktk.TimeSeries()
        self.ts_velocities.time = self.ts_markers.time.copy()
        
        for marker_name in self.marker_names:
            if marker_name in self.ts_markers.data:
                # Extract position data
                positions = self.ts_markers.data[marker_name][:, :3, 3]
                
                # Calculate velocity using finite differences
                dt = np.mean(np.diff(self.ts_markers.time))
                velocities = np.gradient(positions, dt, axis=0)
                
                # Calculate speed (magnitude of velocity)
                speeds = np.linalg.norm(velocities, axis=1)
                
                self.ts_velocities.data[f"{marker_name}_velocity"] = velocities
                self.ts_velocities.data[f"{marker_name}_speed"] = speeds
        
        return self.ts_velocities
    
    def perform_gait_analysis(self):
        """
        Perform basic gait analysis
        
        Returns:
            dict: Gait analysis results
        """
        if self.ts_markers is None:
            raise ValueError("No marker data loaded.")
        
        print("Performing gait analysis...")
        
        gait_results = {}
        
        # Calculate stride length, cadence, etc.
        if 'RCAJ' in self.ts_markers.data and 'LCAJ' in self.ts_markers.data:
            # Extract ankle positions
            right_ankle = self.ts_markers.data['RCAJ'][:, :3, 3]
            left_ankle = self.ts_markers.data['LCAJ'][:, :3, 3]
            
            # Calculate step length (simplified)
            step_lengths = []
            for i in range(1, len(right_ankle)):
                if not (np.isnan(right_ankle[i]).any() or np.isnan(left_ankle[i]).any()):
                    step_length = np.linalg.norm(right_ankle[i] - left_ankle[i-1])
                    step_lengths.append(step_length)
            
            if step_lengths:
                gait_results['average_step_length'] = np.mean(step_lengths)
                gait_results['step_length_variability'] = np.std(step_lengths)
        
        # Calculate walking speed
        if 'SXS' in self.ts_markers.data:  # Sacrum marker for overall movement
            sacrum_pos = self.ts_markers.data['SXS'][:, :3, 3]
            valid_indices = ~np.isnan(sacrum_pos).any(axis=1)
            
            if np.sum(valid_indices) > 1:
                valid_positions = sacrum_pos[valid_indices]
                valid_times = self.ts_markers.time[valid_indices]
                
                # Calculate total distance and time
                total_distance = 0
                for i in range(1, len(valid_positions)):
                    total_distance += np.linalg.norm(valid_positions[i] - valid_positions[i-1])
                
                total_time = valid_times[-1] - valid_times[0]
                if total_time > 0:
                    gait_results['walking_speed'] = total_distance / total_time
        
        self.analysis_results['gait'] = gait_results
        return gait_results

    def analyze_range_of_motion(self):
        """
        Analyze range of motion for joints

        Returns:
            dict: Range of motion analysis results
        """
        if self.ts_angles is None:
            print("Calculating joint angles first...")
            self.calculate_joint_angles()

        print("Analyzing range of motion...")

        rom_results = {}

        for joint_name, angles in self.ts_angles.data.items():
            if isinstance(angles, np.ndarray) and angles.ndim == 1:
                valid_angles = angles[~np.isnan(angles)]
                if len(valid_angles) > 0:
                    rom_results[joint_name] = {
                        'min_angle': np.min(valid_angles),
                        'max_angle': np.max(valid_angles),
                        'range_of_motion': np.max(valid_angles) - np.min(valid_angles),
                        'mean_angle': np.mean(valid_angles),
                        'std_angle': np.std(valid_angles)
                    }

        self.analysis_results['range_of_motion'] = rom_results
        return rom_results

    def detect_events(self, marker_name='RCAJ', axis=2, threshold_method='velocity'):
        """
        Detect gait events (heel strikes, toe offs)

        Args:
            marker_name (str): Marker to use for event detection
            axis (int): Axis to analyze (0=x, 1=y, 2=z)
            threshold_method (str): Method for event detection

        Returns:
            dict: Detected events with timestamps
        """
        if self.ts_markers is None:
            raise ValueError("No marker data loaded.")

        print(f"Detecting events using {marker_name} marker...")

        events = {'heel_strikes': [], 'toe_offs': []}

        if marker_name in self.ts_markers.data:
            # Extract position data
            positions = self.ts_markers.data[marker_name][:, axis, 3]
            valid_indices = ~np.isnan(positions)

            if np.sum(valid_indices) > 10:  # Need sufficient data
                valid_positions = positions[valid_indices]
                valid_times = self.ts_markers.time[valid_indices]

                if threshold_method == 'velocity':
                    # Calculate velocity
                    velocities = np.gradient(valid_positions, np.mean(np.diff(valid_times)))

                    # Find zero crossings in velocity (potential events)
                    zero_crossings = []
                    for i in range(1, len(velocities)):
                        if velocities[i-1] * velocities[i] < 0:  # Sign change
                            zero_crossings.append(i)

                    # Classify events based on position and velocity patterns
                    for crossing in zero_crossings:
                        if crossing > 5 and crossing < len(velocities) - 5:
                            # Simple heuristic: heel strike when moving down, toe off when moving up
                            if velocities[crossing-1] < 0 and velocities[crossing+1] > 0:
                                events['heel_strikes'].append(valid_times[crossing])
                            elif velocities[crossing-1] > 0 and velocities[crossing+1] < 0:
                                events['toe_offs'].append(valid_times[crossing])

        self.analysis_results['events'] = events
        return events

    def generate_report(self):
        """
        Generate a comprehensive analysis report

        Returns:
            dict: Complete analysis report
        """
        print("Generating comprehensive analysis report...")

        report = {
            'file_info': {
                'markers_count': len(self.marker_names),
                'marker_names': self.marker_names,
                'duration': self.ts_markers.time[-1] - self.ts_markers.time[0] if self.ts_markers else 0,
                'sampling_rate': 1 / np.mean(np.diff(self.ts_markers.time)) if self.ts_markers else 0
            }
        }

        # Add all analysis results
        if hasattr(self, 'analysis_results'):
            report.update(self.analysis_results)

        # Calculate additional metrics
        if self.ts_markers:
            # Data quality metrics
            quality_metrics = {}
            for marker in self.marker_names:
                if marker in self.ts_markers.data:
                    positions = self.ts_markers.data[marker][:, :3, 3]
                    valid_data = ~np.isnan(positions).any(axis=1)
                    quality_metrics[marker] = {
                        'data_completeness': np.sum(valid_data) / len(valid_data) * 100,
                        'missing_frames': len(valid_data) - np.sum(valid_data)
                    }

            report['data_quality'] = quality_metrics

        return report

    def visualize_markers_3d(self, frame_range=None, show_trails=True):
        """
        Create 3D visualization of marker positions

        Args:
            frame_range (tuple): (start_frame, end_frame) or None for all frames
            show_trails (bool): Whether to show marker trails

        Returns:
            plotly.graph_objects.Figure: 3D plot
        """
        if self.ts_markers is None:
            raise ValueError("No marker data loaded.")

        fig = go.Figure()

        # Determine frame range
        if frame_range is None:
            start_frame, end_frame = 0, len(self.ts_markers.time)
        else:
            start_frame, end_frame = frame_range

        colors = px.colors.qualitative.Set1

        for i, marker in enumerate(self.marker_names):
            if marker in self.ts_markers.data:
                positions = self.ts_markers.data[marker][start_frame:end_frame, :3, 3]
                valid_indices = ~np.isnan(positions).any(axis=1)

                if np.sum(valid_indices) > 0:
                    valid_positions = positions[valid_indices]
                    color = colors[i % len(colors)]

                    if show_trails and len(valid_positions) > 1:
                        # Add trail
                        fig.add_trace(go.Scatter3d(
                            x=valid_positions[:, 0],
                            y=valid_positions[:, 1],
                            z=valid_positions[:, 2],
                            mode='lines',
                            line=dict(color=color, width=2),
                            name=f'{marker} Trail',
                            opacity=0.6
                        ))

                    # Add current position
                    fig.add_trace(go.Scatter3d(
                        x=[valid_positions[-1, 0]],
                        y=[valid_positions[-1, 1]],
                        z=[valid_positions[-1, 2]],
                        mode='markers',
                        marker=dict(size=8, color=color),
                        name=marker
                    ))

        fig.update_layout(
            title='3D Marker Positions',
            scene=dict(
                xaxis_title='X (m)',
                yaxis_title='Y (m)',
                zaxis_title='Z (m)',
                aspectmode='cube'
            )
        )

        return fig

    def plot_joint_angles(self):
        """
        Plot joint angles over time

        Returns:
            plotly.graph_objects.Figure: Joint angle plots
        """
        if self.ts_angles is None:
            self.calculate_joint_angles()

        if not self.ts_angles.data:
            print("No joint angles calculated.")
            return None

        fig = make_subplots(
            rows=len(self.ts_angles.data),
            cols=1,
            subplot_titles=list(self.ts_angles.data.keys()),
            vertical_spacing=0.1
        )

        for i, (joint_name, angles) in enumerate(self.ts_angles.data.items(), 1):
            if isinstance(angles, np.ndarray) and angles.ndim == 1:
                fig.add_trace(
                    go.Scatter(
                        x=self.ts_angles.time,
                        y=angles,
                        mode='lines',
                        name=joint_name,
                        line=dict(width=2)
                    ),
                    row=i, col=1
                )

        fig.update_layout(
            title='Joint Angles Over Time',
            height=300 * len(self.ts_angles.data),
            showlegend=False
        )

        fig.update_xaxes(title_text='Time (s)')
        fig.update_yaxes(title_text='Angle (degrees)')

        return fig

    def plot_velocities(self, marker_names=None):
        """
        Plot marker velocities

        Args:
            marker_names (list): List of markers to plot, or None for all

        Returns:
            plotly.graph_objects.Figure: Velocity plots
        """
        if self.ts_velocities is None:
            self.calculate_velocities()

        if marker_names is None:
            marker_names = self.marker_names[:5]  # Limit to first 5 markers

        fig = go.Figure()

        colors = px.colors.qualitative.Set1

        for i, marker in enumerate(marker_names):
            speed_key = f"{marker}_speed"
            if speed_key in self.ts_velocities.data:
                speeds = self.ts_velocities.data[speed_key]
                fig.add_trace(go.Scatter(
                    x=self.ts_velocities.time,
                    y=speeds,
                    mode='lines',
                    name=f'{marker} Speed',
                    line=dict(color=colors[i % len(colors)], width=2)
                ))

        fig.update_layout(
            title='Marker Speeds Over Time',
            xaxis_title='Time (s)',
            yaxis_title='Speed (m/s)',
            hovermode='x unified'
        )

        return fig


def main():
    """
    Example usage of the OptitrackKineticsAnalyzer
    """
    # Initialize analyzer
    analyzer = OptitrackKineticsAnalyzer()

    # Example file path - update this to your actual file
    file_path = "data/Take 2025-06-04 11.57.30 AM_ABDADD2.csv"

    try:
        print("=== Optitrack Motion Analysis using Kinetics Toolkit ===\n")

        # Load data
        print("1. Loading Optitrack data...")
        analyzer.load_optitrack_csv(file_path)

        # Calculate joint angles
        print("\n2. Calculating joint angles...")
        analyzer.calculate_joint_angles()

        # Calculate velocities
        print("\n3. Calculating marker velocities...")
        analyzer.calculate_velocities()

        # Perform gait analysis
        print("\n4. Performing gait analysis...")
        gait_results = analyzer.perform_gait_analysis()

        # Analyze range of motion
        print("\n5. Analyzing range of motion...")
        rom_results = analyzer.analyze_range_of_motion()

        # Detect events
        print("\n6. Detecting gait events...")
        events = analyzer.detect_events()

        # Generate comprehensive report
        print("\n7. Generating analysis report...")
        report = analyzer.generate_report()

        # Print summary results
        print("\n=== ANALYSIS RESULTS ===")
        print(f"File Duration: {report['file_info']['duration']:.2f} seconds")
        print(f"Sampling Rate: {report['file_info']['sampling_rate']:.1f} Hz")
        print(f"Number of Markers: {report['file_info']['markers_count']}")

        if 'gait' in report:
            print(f"\nGait Analysis:")
            for key, value in report['gait'].items():
                print(f"  {key}: {value:.4f}")

        if 'range_of_motion' in report:
            print(f"\nRange of Motion:")
            for joint, rom_data in report['range_of_motion'].items():
                print(f"  {joint}: {rom_data['range_of_motion']:.2f}° (min: {rom_data['min_angle']:.2f}°, max: {rom_data['max_angle']:.2f}°)")

        if 'events' in report:
            print(f"\nDetected Events:")
            print(f"  Heel Strikes: {len(report['events']['heel_strikes'])}")
            print(f"  Toe Offs: {len(report['events']['toe_offs'])}")

        # Data quality summary
        if 'data_quality' in report:
            print(f"\nData Quality:")
            avg_completeness = np.mean([metrics['data_completeness'] for metrics in report['data_quality'].values()])
            print(f"  Average Data Completeness: {avg_completeness:.1f}%")

        # Create visualizations
        print("\n8. Creating visualizations...")

        # 3D marker visualization
        fig_3d = analyzer.visualize_markers_3d()
        fig_3d.write_html("optitrack_3d_markers.html")
        print("  Saved 3D marker visualization to: optitrack_3d_markers.html")

        # Joint angle plots
        if analyzer.ts_angles and analyzer.ts_angles.data:
            fig_angles = analyzer.plot_joint_angles()
            if fig_angles:
                fig_angles.write_html("optitrack_joint_angles.html")
                print("  Saved joint angle plots to: optitrack_joint_angles.html")

        # Velocity plots
        fig_velocities = analyzer.plot_velocities()
        fig_velocities.write_html("optitrack_velocities.html")
        print("  Saved velocity plots to: optitrack_velocities.html")

        # Save detailed report to JSON
        import json

        # Convert numpy arrays to lists for JSON serialization
        def convert_numpy(obj):
            if isinstance(obj, np.ndarray):
                return obj.tolist()
            elif isinstance(obj, np.integer):
                return int(obj)
            elif isinstance(obj, np.floating):
                return float(obj)
            elif isinstance(obj, dict):
                return {key: convert_numpy(value) for key, value in obj.items()}
            elif isinstance(obj, list):
                return [convert_numpy(item) for item in obj]
            else:
                return obj

        json_report = convert_numpy(report)

        with open("optitrack_analysis_report.json", "w") as f:
            json.dump(json_report, f, indent=2)
        print("  Saved detailed report to: optitrack_analysis_report.json")

        print("\n=== Analysis Complete! ===")
        print("Check the generated HTML files for interactive visualizations.")

    except FileNotFoundError:
        print(f"Error: Could not find file '{file_path}'")
        print("Please update the file_path variable with the correct path to your Optitrack CSV file.")
    except Exception as e:
        print(f"Error during analysis: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
