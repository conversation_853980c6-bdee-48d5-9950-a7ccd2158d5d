import dash
from dash import dcc, html, Input, Output, callback, State
import plotly.graph_objects as go
import pandas as pd
import numpy as np
from collections import defaultdict
import time

class MotionCaptureApp:
    def __init__(self):
        self.app = dash.Dash(__name__)
        self.data = {}
        self.frame_count = 0
        self.point_names = []
        self.setup_layout()
        self.setup_callbacks()
    
    def load_csv(self, filepath):
        """Load and parse the OptiTrack motion capture CSV file"""
        try:
            print(f"Loading OptiTrack CSV: {filepath}")

            # Manual parsing approach for better control
            with open(filepath, 'r') as file:
                lines = file.readlines()

            print(f"Total lines in file: {len(lines)}")

            # Parse each line manually to handle the specific OptiTrack format
            parsed_lines = []
            for line in lines:
                # Split by comma and clean up
                row_data = [cell.strip() for cell in line.strip().split(',')]
                parsed_lines.append(row_data)

            # OptiTrack CSV structure (0-indexed):
            # Row 0: Metadata
            # Row 1: Empty
            # Row 2: Type row (all "Marker")
            # Row 3: Name row (marker names like <PERSON><PERSON>7, <PERSON><PERSON><PERSON>, etc.)
            # Row 4: ID row
            # Row 5: Position row
            # Row 6: Headers (Frame, Time (Seconds), X, Y, Z, X, Y, Z, ...)
            # Row 7+: Data

            if len(parsed_lines) < 8:
                raise ValueError("File doesn't have enough rows for OptiTrack format")

            # Extract marker names from row 3 (name row)
            name_row = parsed_lines[3]
            print(f"Name row: {name_row[:10]}...")  # Show first 10 for debugging

            # Extract headers from row 6
            header_row = parsed_lines[6]
            print(f"Header row: {header_row[:10]}...")  # Show first 10 for debugging

            # Parse marker names and create mapping
            # Start from column 2 (skip Frame and Time columns)
            self.point_names = []
            point_column_mapping = {}

            # Group columns by marker (every 3 columns = X,Y,Z for one marker)
            col_idx = 2  # Start after Frame and Time columns
            while col_idx < len(name_row):
                if col_idx < len(name_row) and name_row[col_idx]:
                    marker_name = name_row[col_idx]
                    if marker_name and marker_name not in self.point_names:
                        self.point_names.append(marker_name)

                        # Map the next 3 columns to X, Y, Z for this marker
                        if col_idx < len(header_row):
                            point_column_mapping[col_idx] = (marker_name, 'x')
                        if col_idx + 1 < len(header_row):
                            point_column_mapping[col_idx + 1] = (marker_name, 'y')
                        if col_idx + 2 < len(header_row):
                            point_column_mapping[col_idx + 2] = (marker_name, 'z')

                col_idx += 3  # Move to next marker (skip X, Y, Z columns)

            print(f"Found markers: {self.point_names}")
            print(f"Point-column mapping created for {len(point_column_mapping)} columns")

            # Initialize data structure
            self.data = {}
            for point_name in self.point_names:
                self.data[point_name] = {'x': [], 'y': [], 'z': []}

            # Extract data starting from row 7 (data rows)
            frame_count = 0
            data_start_row = 7

            for i in range(data_start_row, len(parsed_lines)):
                row = parsed_lines[i]

                # Skip empty rows
                if not row or not row[0]:
                    continue

                # Skip rows with invalid frame numbers
                try:
                    frame_num = float(row[0]) if row[0] else None
                    if frame_num is None:
                        continue
                except (ValueError, IndexError):
                    continue

                # Process each mapped column for this frame
                for col_idx, (point_name, coord) in point_column_mapping.items():
                    if col_idx < len(row) and row[col_idx]:
                        try:
                            value = float(row[col_idx])
                        except (ValueError, TypeError):
                            value = 0.0
                    else:
                        value = 0.0

                    self.data[point_name][coord].append(value)

                frame_count += 1
            
            self.frame_count = frame_count
            print(f"Loaded {len(self.point_names)} points with {self.frame_count} frames")
            
            # Verify data integrity
            for point_name in self.point_names:
                x_len = len(self.data[point_name]['x'])
                y_len = len(self.data[point_name]['y'])
                z_len = len(self.data[point_name]['z'])
                print(f"{point_name}: X={x_len}, Y={y_len}, Z={z_len} frames")
                
                # Ensure all coordinates have the same length
                min_len = min(x_len, y_len, z_len)
                if min_len < max(x_len, y_len, z_len):
                    print(f"Warning: Trimming {point_name} to {min_len} frames for consistency")
                    self.data[point_name]['x'] = self.data[point_name]['x'][:min_len]
                    self.data[point_name]['y'] = self.data[point_name]['y'][:min_len]
                    self.data[point_name]['z'] = self.data[point_name]['z'][:min_len]
            
            return self.data
            
        except Exception as e:
            raise ValueError(f"Could not read CSV file: {e}")
    
    def setup_layout(self):
        """Setup the Dash app layout"""
        self.app.layout = html.Div([
            html.H1("3D Motion Capture Animation", style={'textAlign': 'center'}),
            
            html.Div([
                html.Div([
                    html.Label("Upload CSV File:"),
                    dcc.Upload(
                        id='upload-data',
                        children=html.Div(['Drag and Drop or ', html.A('Select Files')]),
                        style={
                            'width': '100%', 'height': '60px', 'lineHeight': '60px',
                            'borderWidth': '1px', 'borderStyle': 'dashed',
                            'borderRadius': '5px', 'textAlign': 'center',
                            'margin': '10px'
                        },
                        multiple=False
                    ),
                ], style={'width': '48%', 'display': 'inline-block'}),
                
                html.Div([
                    html.Label("CSV File Path (for testing):"),
                    dcc.Input(
                        id='file-path',
                        type='text',
                        placeholder='Enter path to CSV file...',
                        style={'width': '70%', 'marginRight': '10px'}
                    ),
                    html.Button('Load File', id='load-button', n_clicks=0),
                ], style={'width': '48%', 'float': 'right', 'display': 'inline-block'}),
            ]),
            
            html.Hr(),
            
            html.Div([
                html.Div([
                    html.Button('Play', id='play-button', n_clicks=0, 
                               style={'marginRight': '10px'}),
                    html.Button('Pause', id='pause-button', n_clicks=0,
                               style={'marginRight': '10px'}),
                    html.Button('Reset', id='reset-button', n_clicks=0,
                               style={'marginRight': '20px'}),
                    
                    html.Label('Show Trails:', style={'marginRight': '10px'}),
                    dcc.Checklist(
                        id='trail-checkbox',
                        options=[{'label': 'Enable Trails', 'value': 'trails'}],
                        value=[],
                        inline=True,
                        style={'marginRight': '20px'}
                    ),
                    
                    html.Label('Speed:', style={'marginRight': '10px'}),
                    dcc.Slider(
                        id='speed-slider',
                        min=0.1,
                        max=3.0,
                        step=0.1,
                        value=1.0,
                        marks={i: f'{i}x' for i in [0.5, 1.0, 1.5, 2.0, 2.5, 3.0]},
                        tooltip={"placement": "bottom", "always_visible": True}
                    ),
                ], style={'textAlign': 'center', 'marginBottom': '20px'}),
                
                html.Div([
                    html.Label('Frame:', style={'marginRight': '10px'}),
                    dcc.Slider(
                        id='frame-slider',
                        min=0,
                        max=100,
                        step=1,
                        value=0,
                        marks={},
                        tooltip={"placement": "bottom", "always_visible": True}
                    ),
                ], style={'margin': '20px'}),
            ]),
            
            dcc.Graph(id='3d-plot', style={'height': '70vh'}),
            
            # Hidden divs to store data and state
            html.Div(id='data-store', style={'display': 'none'}),
            html.Div(id='animation-state', style={'display': 'none'}),
            
            # Interval component for animation
            dcc.Interval(
                id='animation-interval',
                interval=100,  # milliseconds
                n_intervals=0,
                disabled=True
            ),
        ])
    
    def setup_callbacks(self):
        """Setup all the callback functions"""
        
        @self.app.callback(
            [Output('data-store', 'children'),
             Output('frame-slider', 'max'),
             Output('frame-slider', 'marks')],
            [Input('load-button', 'n_clicks')],
            [State('file-path', 'value')]
        )
        def load_data(n_clicks, filepath):
            if n_clicks > 0 and filepath:
                try:
                    data = self.load_csv(filepath)
                    marks = {i: str(i) if i % (self.frame_count // 10) == 0 else '' 
                            for i in range(0, self.frame_count)}
                    return str(data), self.frame_count - 1, marks
                except Exception as e:
                    print(f"Error loading file: {e}")
                    return '', 100, {}
            return '', 100, {}
        
        @self.app.callback(
            [Output('animation-interval', 'disabled'),
             Output('animation-interval', 'interval')],
            [Input('play-button', 'n_clicks'),
             Input('pause-button', 'n_clicks'),
             Input('speed-slider', 'value')]
        )
        def control_animation(play_clicks, pause_clicks, speed):
            ctx = dash.callback_context
            if not ctx.triggered:
                return True, 100
            
            trigger_id = ctx.triggered[0]['prop_id'].split('.')[0]
            interval = int(100 / speed)  # Adjust interval based on speed
            
            if trigger_id == 'play-button':
                return False, interval
            elif trigger_id == 'pause-button':
                return True, interval
            else:  # speed changed
                # Keep current state but update interval
                return dash.no_update, interval
        
        @self.app.callback(
            Output('frame-slider', 'value'),
            [Input('animation-interval', 'n_intervals'),
             Input('reset-button', 'n_clicks')],
            [State('frame-slider', 'value'),
             State('frame-slider', 'max')]
        )
        def update_frame(n_intervals, reset_clicks, current_frame, max_frame):
            ctx = dash.callback_context
            if not ctx.triggered:
                return 0
            
            trigger_id = ctx.triggered[0]['prop_id'].split('.')[0]
            
            if trigger_id == 'reset-button':
                return 0
            elif trigger_id == 'animation-interval':
                return (current_frame + 1) % (max_frame + 1)
            
            return current_frame
        
        @self.app.callback(
            Output('3d-plot', 'figure'),
            [Input('frame-slider', 'value'),
             Input('trail-checkbox', 'value')],
            [State('data-store', 'children')]
        )
        def update_3d_plot(frame, trail_options, data_str):
            if not data_str or not self.data:
                return go.Figure()
            
            show_trails = 'trails' in trail_options
            
            fig = go.Figure()
            
            # Define colors for different points
            colors = ['red', 'blue', 'green', 'orange', 'purple', 'brown', 'pink', 'gray', 'olive', 'cyan']
            
            for i, point_name in enumerate(self.point_names):
                if point_name in self.data:
                    point_data = self.data[point_name]
                    color = colors[i % len(colors)]
                    
                    if show_trails and frame > 0:
                        # Show trail (line from start to current frame)
                        trail_x = point_data['x'][:frame+1]
                        trail_y = point_data['y'][:frame+1]
                        trail_z = point_data['z'][:frame+1]
                        
                        fig.add_trace(go.Scatter3d(
                            x=trail_x,
                            y=trail_y,
                            z=trail_z,
                            mode='lines',
                            line=dict(color=color, width=2),
                            name=f'{point_name}_trail',
                            showlegend=False,
                            opacity=0.6
                        ))
                    
                    # Current point position
                    if frame < len(point_data['x']):
                        fig.add_trace(go.Scatter3d(
                            x=[point_data['x'][frame]],
                            y=[point_data['y'][frame]],
                            z=[point_data['z'][frame]],
                            mode='markers',
                            marker=dict(color=color, size=8),
                            name=point_name,
                            showlegend=True
                        ))
            
            # Update layout
            fig.update_layout(
                title=f'Frame {frame}',
                scene=dict(
                    xaxis_title='X (meters)',
                    yaxis_title='Y (meters)',
                    zaxis_title='Z (meters)',
                    aspectmode='cube'
                ),
                height=600,
                showlegend=True
            )
            
            return fig
    
    def run(self, debug=True, port=8050):
        """Run the Dash app"""
        self.app.run(debug=debug, port=port)

# Example usage
if __name__ == '__main__':
    app = MotionCaptureApp()
    
    # You can preload a file for testing
    # app.load_csv('path_to_your_optitrack_file.csv')
    
    app.run(debug=True)

# Example of how to use this with your OptiTrack CSV file:
"""
# Create the app
mocap_app = MotionCaptureApp()

# Load your CSV file
mocap_app.load_csv('your_optitrack_file.csv')

# Run the app
mocap_app.run(debug=True, port=8050)

# Then open http://localhost:8050 in your browser
"""