import dash
from dash import dcc, html, callback, Input, Output
import pandas as pd
import plotly.express as px
import os
import glob

# Initialize the Dash app
app = dash.Dash(__name__)

# Function to get list of files in directory
def get_file_options(directory, file_types=['*.csv']):
    files = []
    for file_type in file_types:
        pattern = os.path.join(directory, file_type)
        files.extend(glob.glob(pattern))
    
    # Create dropdown options list
    options = []
    for file_path in files:
        # Get just the filename without path or extension for the label
        filename = os.path.basename(file_path)
        options.append({'label': filename, 'value': file_path})
    
    return options

# Define the app layout
app.layout = html.Div([
    html.H1("Dynamic Data File Selector"),
    
    # Dropdown menu populated with files from directory
    dcc.Dropdown(
        id='file-dropdown',
        options=get_file_options('data/'),  # Specify your data directory here
        value=None,  # No default selection
        placeholder="Select a data file"
    ),
    
    # Graph to display the selected data
    dcc.Graph(id='data-graph')
])

# Callback to update the graph based on selected file
@callback(
    Output('data-graph', 'figure'),
    Input('file-dropdown', 'value')
)
def update_graph(selected_file):
    if selected_file is None:
        # Return empty figure if no file is selected
        return px.scatter(title="Please select a data file")
    
    try:
        # Try to load the selected data file
        df = pd.read_csv(selected_file)
        
        # Create a figure using the data
        # This is a simple example - adjust according to your data
        if 'date' in df.columns and 'value' in df.columns:
            fig = px.line(df, x='date', y='value', title=f'Data from {os.path.basename(selected_file)}')
        else:
            # Fallback for different data structures
            fig = px.scatter(df, title=f'Data from {os.path.basename(selected_file)}')
            
        return fig
    except Exception as e:
        # Handle errors gracefully
        return px.scatter(title=f"Error loading file: {str(e)}")

# Run the app
if __name__ == '__main__':
    app.run_server(debug=True)