import pandas as pd
import plotly.graph_objects as go
import plotly.express as px
from dash import Dash, dcc, html, Input, Output, State, callback_context, ctx
import numpy as np
from collections import defaultdict
import re
from scipy import interpolate

class OptitrackAnimationDashboard:
    def __init__(self):
        self.app = Dash(__name__)
        self.datasets = {
            'dataset1': {
                'data': None,
                'point_data': {},
                'frame_count': 0,
                'point_names': [],
                'skeleton_connections': [],
                'format_type': 'Optitrack',
                'name': 'Dataset 1',
                'timestamps': [],
                'sampling_rate': None,
                'duration': 0.0,
                'resampled_data': {},
                'data_bounds': {'x_min': 0, 'x_max': 0, 'y_min': 0, 'y_max': 0, 'z_min': 0, 'z_max': 0}
            },
            'dataset2': {
                'data': None,
                'point_data': {},
                'frame_count': 0,
                'point_names': [],
                'skeleton_connections': [],
                'format_type': 'Optitrack',
                'name': 'Dataset 2',
                'timestamps': [],
                'sampling_rate': None,
                'duration': 0.0,
                'resampled_data': {},
                'data_bounds': {'x_min': 0, 'x_max': 0, 'y_min': 0, 'y_max': 0, 'z_min': 0, 'z_max': 0}
            },
            'dataset3': {
                'data': None,
                'point_data': {},
                'frame_count': 0,
                'point_names': [],
                'skeleton_connections': [],
                'format_type': 'Optitrack',
                'name': 'Dataset 3',
                'timestamps': [],
                'sampling_rate': None,
                'duration': 0.0,
                'resampled_data': {},
                'data_bounds': {'x_min': 0, 'x_max': 0, 'y_min': 0, 'y_max': 0, 'z_min': 0, 'z_max': 0}
            }
        }
        self.setup_layout()
        self.setup_callbacks()
    
    def get_human_readable_name(self, marker_name):
        """Convert technical Optitrack marker names to human-readable labels"""
        # Dictionary mapping common Optitrack marker names to human-readable labels
        marker_mapping = {
            # Head and Neck
            'CV7': 'Neck (C7)',
            'SJN': 'Throat (Suprasternal)',
            'HEAD': 'Head',
            'FOREHEAD': 'Forehead',

            # Spine
            'TV2': 'Upper Back (T2)',
            'TV7': 'Mid Back (T7)',
            'TV10': 'Lower Back (T10)',
            'SXS': 'Lower Back (Sacrum)',
            'RRSP': 'Right Spine',
            'LRSP': 'Left Spine',

            # Right Arm
            'RUA': 'Right Upper Arm',
            'RUSP': 'Right Shoulder',
            'RHLE': 'Right Elbow (Lateral)',
            'RHME': 'Right Elbow (Medial)',
            'RFA': 'Right Forearm',
            'RWRA': 'Right Wrist (Radial)',
            'RWRU': 'Right Wrist (Ulnar)',
            'RHAND': 'Right Hand',
            'RFIN': 'Right Fingers',

            # Left Arm
            'LUA': 'Left Upper Arm',
            'LUSP': 'Left Shoulder',
            'LHLE': 'Left Elbow (Lateral)',
            'LHME': 'Left Elbow (Medial)',
            'LFA': 'Left Forearm',
            'LWRA': 'Left Wrist (Radial)',
            'LWRU': 'Left Wrist (Ulnar)',
            'LHAND': 'Left Hand',
            'LFIN': 'Left Fingers',

            # Right Leg
            'RHGT': 'Right Hip',
            'RHM1': 'Right Upper Thigh',
            'RHM2': 'Right Mid Thigh',
            'RHM3': 'Right Lower Thigh',
            'RKNE': 'Right Knee',
            'RCAJ': 'Right Ankle',
            'RTOE': 'Right Toe',
            'RHEE': 'Right Heel',
            'RFOOT': 'Right Foot',

            # Left Leg
            'LHGT': 'Left Hip',
            'LHM1': 'Left Upper Thigh',
            'LHM2': 'Left Mid Thigh',
            'LHM3': 'Left Lower Thigh',
            'LKNE': 'Left Knee',
            'LCAJ': 'Left Ankle',
            'LTOE': 'Left Toe',
            'LHEE': 'Left Heel',
            'LFOOT': 'Left Foot',

            # Pelvis
            'RASI': 'Right Hip Front',
            'LASI': 'Left Hip Front',
            'RPSI': 'Right Hip Back',
            'LPSI': 'Left Hip Back',

            # Additional common markers
            'CLAV': 'Collarbone',
            'STER': 'Sternum',
            'XPHO': 'Lower Chest',
            'STRN': 'Sternum',
            'RBAK': 'Right Back',
            'LBAK': 'Left Back',

            # Generic patterns for numbered markers
            'C7': 'Neck (C7)',
            'T2': 'Upper Back (T2)',
            'T7': 'Mid Back (T7)',
            'T10': 'Lower Back (T10)',
        }

        # First try exact match
        if marker_name in marker_mapping:
            return marker_mapping[marker_name]

        # Try case-insensitive match
        marker_upper = marker_name.upper()
        if marker_upper in marker_mapping:
            return marker_mapping[marker_upper]

        # Try partial matches for complex marker names
        for key, value in marker_mapping.items():
            if key in marker_upper or marker_upper in key:
                return value

        # If no mapping found, try to make it more readable by adding spaces
        # Convert camelCase or underscores to spaced words
        readable_name = marker_name.replace('_', ' ').replace('-', ' ')

        # Add spaces before capital letters (for camelCase)
        import re
        readable_name = re.sub(r'([a-z])([A-Z])', r'\1 \2', readable_name)

        # Capitalize first letter of each word
        readable_name = ' '.join(word.capitalize() for word in readable_name.split())

        return readable_name

    def find_joint_by_patterns(self, patterns, point_names):
        """Find a joint name that matches any of the given patterns (case-insensitive)"""
        for pattern in patterns:
            # Try exact match first
            if pattern in point_names:
                return pattern
            # Try case-insensitive match
            for joint in point_names:
                if joint.lower() == pattern.lower():
                    return joint
            # Try partial match (contains pattern)
            for joint in point_names:
                if pattern.lower() in joint.lower():
                    return joint
        return None
    
    def define_optitrack_skeleton(self, point_names):
        """Define skeleton connections for Optitrack format based on marker names"""
        # Optitrack typically uses anatomical marker names
        # Map common Optitrack markers to skeleton structure
        skeleton_structure = [
            # Spine chain
            {'from': ['CV7'], 'to': ['TV2']},  # Cervical 7 to Thoracic 2
            {'from': ['TV2'], 'to': ['TV7']},  # Thoracic 2 to Thoracic 7
            {'from': ['TV7'], 'to': ['SXS']},  # Thoracic 7 to Sacrum
            
            # Head/Neck
            {'from': ['SJN'], 'to': ['CV7']},  # Suprasternal notch to C7
            
            # Arms - if we have upper arm and forearm markers
            {'from': ['RUA'], 'to': ['RUSP']},  # Right upper arm connections
            {'from': ['RUSP'], 'to': ['RHLE']}, # Right upper arm to elbow
            {'from': ['RHLE'], 'to': ['RHME']}, # Right elbow connections
            
            # Legs - Hip to knee connections
            {'from': ['RHGT'], 'to': ['RHM2']}, # Right hip to mid-thigh
            {'from': ['RHM2'], 'to': ['RCAJ']}, # Right mid-thigh to ankle
            {'from': ['LHGT'], 'to': ['LCAJ']}, # Left hip to ankle
            
            # Pelvis connections
            {'from': ['RHGT'], 'to': ['LHGT']}, # Right hip to left hip
            {'from': ['SXS'], 'to': ['RHGT']},  # Sacrum to right hip
            {'from': ['SXS'], 'to': ['LHGT']},  # Sacrum to left hip
            
            # Spine connections
            {'from': ['RRSP'], 'to': ['SXS']},  # Right rear spine to sacrum
        ]
        
        return self.build_skeleton_connections(skeleton_structure, point_names)
    
    def resample_dataset_to_time(self, dataset_key, target_timestamps):
        """Resample a dataset to match target timestamps using interpolation"""
        dataset = self.datasets[dataset_key]
        if not dataset['point_data'] or not dataset['timestamps']:
            return {}
        
        resampled_data = {}
        source_timestamps = np.array(dataset['timestamps'])
        target_timestamps = np.array(target_timestamps)
        
        for point_name, coords in dataset['point_data'].items():
            resampled_data[point_name] = {'x': [], 'y': [], 'z': []}
            
            for axis in ['x', 'y', 'z']:
                source_values = np.array(coords[axis])
                
                # Find valid (non-None) data points
                valid_mask = ~pd.isna(source_values)
                if np.sum(valid_mask) < 2:  # Need at least 2 points for interpolation
                    # Fill with None if insufficient data
                    resampled_data[point_name][axis] = [None] * len(target_timestamps)
                    continue
                
                valid_times = source_timestamps[valid_mask]
                valid_values = source_values[valid_mask]
                
                # Interpolate to target timestamps
                try:
                    # Use linear interpolation, extrapolate with nearest values
                    f = interpolate.interp1d(valid_times, valid_values, 
                                           kind='linear', bounds_error=False, 
                                           fill_value=(valid_values[0], valid_values[-1]))
                    interpolated_values = f(target_timestamps)
                    resampled_data[point_name][axis] = interpolated_values.tolist()
                except Exception as e:
                    print(f"Interpolation failed for {point_name}.{axis}: {e}")
                    resampled_data[point_name][axis] = [None] * len(target_timestamps)
        
        return resampled_data
    
    def synchronize_datasets(self):
        """Synchronize datasets by resampling to a common timeline"""
        # Find datasets with valid data
        valid_datasets = []
        for key, dataset in self.datasets.items():
            if dataset['point_data'] and dataset['timestamps']:
                valid_datasets.append(key)
        
        if len(valid_datasets) < 2:
            return  # Nothing to synchronize
        
        # Find the dataset with the lowest sampling rate (longest time intervals)
        min_sampling_rate = float('inf')
        reference_dataset = None
        
        for key in valid_datasets:
            dataset = self.datasets[key]
            if dataset['sampling_rate'] and dataset['sampling_rate'] < min_sampling_rate:
                min_sampling_rate = dataset['sampling_rate']
                reference_dataset = key
        
        if not reference_dataset:
            return
        
        # Use the reference dataset's timestamps as the target
        target_timestamps = self.datasets[reference_dataset]['timestamps']
        
        # Resample all other datasets to match the reference timeline
        for key in valid_datasets:
            if key != reference_dataset:
                print(f"Resampling {key} to match {reference_dataset} timeline...")
                print(f"  Original {key}: {self.datasets[key]['frame_count']} frames at {self.datasets[key]['sampling_rate']:.1f} Hz")
                print(f"  Target timeline: {len(target_timestamps)} frames")
                resampled_data = self.resample_dataset_to_time(key, target_timestamps)
                self.datasets[key]['resampled_data'] = resampled_data
                print(f"  Resampled {key}: {len(target_timestamps)} synchronized frames")
        
        # The reference dataset uses its original data
        self.datasets[reference_dataset]['resampled_data'] = self.datasets[reference_dataset]['point_data']
        print(f"  Reference {reference_dataset}: using original {len(target_timestamps)} frames")
        
        print(f"Synchronization complete. Reference: {reference_dataset}")
        print(f"All datasets now have {len(target_timestamps)} synchronized frames")
    
    def build_skeleton_connections(self, skeleton_structure, point_names):
        """Build skeleton connections based on available joints"""
        connections = []
        
        print(f"\nProcessing {len(skeleton_structure)} potential connections...")
        
        for connection_def in skeleton_structure:
            from_joint = self.find_joint_by_patterns(connection_def['from'], point_names)
            to_joint = self.find_joint_by_patterns(connection_def['to'], point_names)
            
            if from_joint and to_joint:
                # Avoid duplicate connections
                if (from_joint, to_joint) not in connections and (to_joint, from_joint) not in connections:
                    connections.append((from_joint, to_joint))
                    print(f"  ✓ Connected: {from_joint} -> {to_joint}")
            else:
                missing = []
                if not from_joint:
                    missing.append(f"from={connection_def['from'][0]}")
                if not to_joint:
                    missing.append(f"to={connection_def['to'][0]}")
                print(f"  ✗ Skipped: {', '.join(missing)} (missing joints)")
        
        print(f"\nSuccessfully created {len(connections)} connections")
        return connections

    def calculate_data_bounds(self, point_data):
        """Calculate the global min/max bounds for all coordinate data"""
        all_x_coords = []
        all_y_coords = []
        all_z_coords = []

        for point_name, coords in point_data.items():
            # Collect all valid (non-zero, non-None) coordinates
            for x, y, z in zip(coords['x'], coords['y'], coords['z']):
                if (x is not None and y is not None and z is not None and
                    not (x == 0.0 and y == 0.0 and z == 0.0)):
                    all_x_coords.append(x)
                    all_y_coords.append(y)
                    all_z_coords.append(z)

        if all_x_coords and all_y_coords and all_z_coords:
            bounds = {
                'x_min': min(all_x_coords),
                'x_max': max(all_x_coords),
                'y_min': min(all_y_coords),
                'y_max': max(all_y_coords),
                'z_min': min(all_z_coords),
                'z_max': max(all_z_coords)
            }
            print(f"Calculated data bounds:")
            print(f"  X: [{bounds['x_min']:.3f}, {bounds['x_max']:.3f}]")
            print(f"  Y: [{bounds['y_min']:.3f}, {bounds['y_max']:.3f}]")
            print(f"  Z: [{bounds['z_min']:.3f}, {bounds['z_max']:.3f}]")
            return bounds
        else:
            # Fallback bounds if no valid data
            return {'x_min': -1, 'x_max': 1, 'y_min': 0, 'y_max': 1, 'z_min': -0.5, 'z_max': 0.5}

    def load_optitrack_csv(self, file_path, dataset_key):
        """Load Optitrack CSV file and parse skeletal data using manual parsing"""
        print(f"Loading OptiTrack CSV: {file_path}")

        # Manual parsing approach for better control
        with open(file_path, 'r') as file:
            lines = file.readlines()

        print(f"Total lines in file: {len(lines)}")

        # Parse each line manually to handle the specific OptiTrack format
        parsed_lines = []
        for line in lines:
            # Split by comma and clean up
            row_data = [cell.strip() for cell in line.strip().split(',')]
            parsed_lines.append(row_data)

        # OptiTrack CSV structure (0-indexed):
        # Row 0: Metadata
        # Row 1: Empty
        # Row 2: Type row (all "Marker")
        # Row 3: Name row (marker names like CV7, LCAJ, etc.)
        # Row 4: ID row
        # Row 5: Position row
        # Row 6: Headers (Frame, Time (Seconds), X, Y, Z, X, Y, Z, ...)
        # Row 7+: Data

        if len(parsed_lines) < 8:
            raise ValueError("File doesn't have enough rows for OptiTrack format")

        # Extract marker names from row 3 (name row)
        name_row = parsed_lines[3]
        print(f"Name row: {name_row[:10]}...")  # Show first 10 for debugging

        # Extract headers from row 6
        header_row = parsed_lines[6]
        print(f"Header row: {header_row[:10]}...")  # Show first 10 for debugging

        # Parse marker names and create mapping
        # Start from column 2 (skip Frame and Time columns)
        point_names = []
        point_column_mapping = {}

        # Group columns by marker (every 3 columns = X,Y,Z for one marker)
        col_idx = 2  # Start after Frame and Time columns
        while col_idx < len(name_row):
            if col_idx < len(name_row) and name_row[col_idx]:
                marker_name = name_row[col_idx]
                if marker_name and marker_name not in point_names:
                    point_names.append(marker_name)

                    # Map the next 3 columns to X, Y, Z for this marker
                    if col_idx < len(header_row):
                        point_column_mapping[col_idx] = (marker_name, 'x')
                    if col_idx + 1 < len(header_row):
                        point_column_mapping[col_idx + 1] = (marker_name, 'y')
                    if col_idx + 2 < len(header_row):
                        point_column_mapping[col_idx + 2] = (marker_name, 'z')

            col_idx += 3  # Move to next marker (skip X, Y, Z columns)

        print(f"Found markers: {point_names}")
        print(f"Point-column mapping created for {len(point_column_mapping)} columns")

        # Store dataset info
        self.datasets[dataset_key]['point_names'] = point_names
        self.datasets[dataset_key]['skeleton_connections'] = self.define_optitrack_skeleton(point_names)
        self.datasets[dataset_key]['format_type'] = 'Optitrack'

        # Initialize data structure
        point_data = {name: {'x': [], 'y': [], 'z': []} for name in point_names}
        timestamps = []

        # Extract data starting from row 7 (data rows)
        frame_count = 0
        data_start_row = 7

        for i in range(data_start_row, len(parsed_lines)):
            row = parsed_lines[i]

            # Skip empty rows
            if not row or not row[0]:
                continue

            # Skip rows with invalid frame numbers
            try:
                frame_num = float(row[0]) if row[0] else None
                if frame_num is None:
                    continue

                # Extract timestamp from second column
                timestamp = float(row[1]) if len(row) > 1 and row[1] else frame_count / 120.0
            except (ValueError, IndexError):
                continue

            # Process each mapped column for this frame
            for col_idx, (point_name, coord) in point_column_mapping.items():
                if col_idx < len(row) and row[col_idx]:
                    try:
                        value = float(row[col_idx])
                    except (ValueError, TypeError):
                        value = 0.0
                else:
                    value = 0.0

                point_data[point_name][coord].append(value)

            timestamps.append(timestamp)
            frame_count += 1

        # Calculate sampling rate and duration
        if len(timestamps) > 1:
            try:
                # Convert timestamps to numeric values
                numeric_timestamps = [float(t) if pd.notna(t) else 0.0 for t in timestamps]
                time_diffs = np.diff(numeric_timestamps)
                avg_time_diff = np.mean(time_diffs)
                sampling_rate = 1.0 / avg_time_diff if avg_time_diff > 0 else 120.0
                duration = numeric_timestamps[-1] - numeric_timestamps[0] if numeric_timestamps else 0.0
                timestamps = numeric_timestamps  # Update with numeric values
            except (ValueError, TypeError):
                sampling_rate = 120.0  # Default assumption for Optitrack
                duration = 0.0
                timestamps = list(range(len(timestamps)))  # Use frame indices as timestamps
        else:
            sampling_rate = 120.0  # Default assumption for Optitrack
            duration = 0.0

        self.datasets[dataset_key]['point_data'] = point_data
        self.datasets[dataset_key]['frame_count'] = frame_count
        self.datasets[dataset_key]['timestamps'] = timestamps
        self.datasets[dataset_key]['sampling_rate'] = sampling_rate
        self.datasets[dataset_key]['duration'] = duration

        # Calculate and store global data bounds
        self.datasets[dataset_key]['data_bounds'] = self.calculate_data_bounds(point_data)

        print(f"Loaded Optitrack: {frame_count} frames with {len(point_names)} points")
        print(f"  Sampling rate: {sampling_rate:.1f} Hz, Duration: {duration:.2f}s")

    def load_csv(self, file_path, dataset_key):
        """Load Optitrack CSV file"""
        print(f"Loading Optitrack CSV: {file_path}")
        self.load_optitrack_csv(file_path, dataset_key)

        # Trigger synchronization after loading
        self.synchronize_datasets()

    def setup_layout(self):
        """Setup the Dash layout with triple dataset support"""
        self.app.layout = html.Div([
            html.H1("3D Skeletal Animation Dashboard - Triple Optitrack Dataset Support", style={'textAlign': 'center'}),

            # Dataset 1 Controls
            html.Div([
                html.H3("Dataset 1", style={'color': 'blue'}),
                html.Div([
                    html.Label("Optitrack CSV File Path:"),
                    dcc.Input(
                        id='file-path-input-1',
                        type='text',
                        placeholder='Enter path to Optitrack CSV file',
                        value='data/Take 2025-06-04 11.57.30 AM_ABDADD2.csv',
                        style={'width': '400px', 'marginRight': '10px'}
                    ),
                    html.Button('Load Dataset 1', id='load-button-1', n_clicks=0),
                    html.Div(id='format-display-1', style={'marginTop': '10px', 'fontStyle': 'italic'})
                ], style={'margin': '10px'}),
            ], style={'border': '2px solid blue', 'margin': '10px', 'padding': '10px', 'borderRadius': '5px'}),

            # Dataset 2 Controls
            html.Div([
                html.H3("Dataset 2", style={'color': 'red'}),
                html.Div([
                    html.Label("Optitrack CSV File Path:"),
                    dcc.Input(
                        id='file-path-input-2',
                        type='text',
                        placeholder='Enter path to Optitrack CSV file',
                        value='',
                        style={'width': '400px', 'marginRight': '10px'}
                    ),
                    html.Button('Load Dataset 2', id='load-button-2', n_clicks=0),
                    html.Div(id='format-display-2', style={'marginTop': '10px', 'fontStyle': 'italic'})
                ], style={'margin': '10px'}),
            ], style={'border': '2px solid red', 'margin': '10px', 'padding': '10px', 'borderRadius': '5px'}),

            # Dataset 3 Controls
            html.Div([
                html.H3("Dataset 3", style={'color': 'green'}),
                html.Div([
                    html.Label("Optitrack CSV File Path:"),
                    dcc.Input(
                        id='file-path-input-3',
                        type='text',
                        placeholder='Enter path to Optitrack CSV file',
                        value='',
                        style={'width': '400px', 'marginRight': '10px'}
                    ),
                    html.Button('Load Dataset 3', id='load-button-3', n_clicks=0),
                    html.Div(id='format-display-3', style={'marginTop': '10px', 'fontStyle': 'italic'})
                ], style={'margin': '10px'}),
            ], style={'border': '2px solid green', 'margin': '10px', 'padding': '10px', 'borderRadius': '5px'}),

            # Display Controls
            html.Div([
                html.Label('Display Options:', style={'fontWeight': 'bold', 'marginRight': '20px'}),
                dcc.Checklist(
                    id='dataset-display-checkbox',
                    options=[
                        {'label': 'Show Dataset 1', 'value': 'dataset1'},
                        {'label': 'Show Dataset 2', 'value': 'dataset2'},
                        {'label': 'Show Dataset 3', 'value': 'dataset3'}
                    ],
                    value=['dataset1'],
                    inline=True,
                    style={'marginRight': '20px'}
                ),

                html.Label('Show Trails:', style={'marginRight': '10px'}),
                dcc.Checklist(
                    id='trail-checkbox',
                    options=[{'label': 'Enable Trails', 'value': 'trails'}],
                    value=[],
                    inline=True,
                    style={'marginRight': '20px'}
                ),

                html.Label('Show Skeleton:', style={'marginRight': '10px'}),
                dcc.Checklist(
                    id='skeleton-checkbox',
                    options=[{'label': 'Enable Wireframe', 'value': 'skeleton'}],
                    value=['skeleton'],
                    inline=True,
                    style={'marginRight': '20px'}
                ),

                html.Label('Sync Mode:', style={'marginRight': '10px'}),
                dcc.RadioItems(
                    id='sync-mode-radio',
                    options=[
                        {'label': 'Time-based', 'value': 'time'},
                        {'label': 'Frame-based', 'value': 'frame'}
                    ],
                    value='time',
                    inline=True,
                ),
            ], style={'margin': '20px', 'textAlign': 'center'}),

            # Animation Controls
            html.Div([
                html.Button('Play', id='play-button', n_clicks=0, style={'marginRight': '10px'}),
                html.Button('Pause', id='pause-button', n_clicks=0, style={'marginRight': '10px'}),
                html.Button('Reset', id='reset-button', n_clicks=0, style={'marginRight': '20px'}),

                html.Label('Animation Speed:', style={'marginRight': '10px'}),
                dcc.Slider(
                    id='speed-slider',
                    min=25,
                    max=1000,
                    value=200,
                    marks={25: '0.25x', 50: '0.5x', 100: '1x', 200: '2x', 400: '4x', 600: '6x', 800: '8x', 1000: '10x'},
                    tooltip={"placement": "bottom", "always_visible": True}
                ),
            ], style={'margin': '20px', 'textAlign': 'center'}),

            dcc.Graph(id='3d-plot', style={'height': '600px'}),

            html.Div([
                html.Label('Frame:'),
                dcc.Slider(
                    id='frame-slider',
                    min=0,
                    max=100,
                    value=0,
                    marks={},
                    tooltip={"placement": "bottom", "always_visible": True}
                ),
            ], style={'margin': '20px'}),

            dcc.Interval(
                id='interval-component',
                interval=200,
                n_intervals=0,
                disabled=True
            ),

            # Data stores
            dcc.Store(id='animation-state', data={'playing': False, 'current_frame': 0}),
            dcc.Store(id='data-store-1', data={}),
            dcc.Store(id='data-store-2', data={}),
            dcc.Store(id='data-store-3', data={}),
            dcc.Store(id='skeleton-store-1', data=[]),
            dcc.Store(id='skeleton-store-2', data=[]),
            dcc.Store(id='skeleton-store-3', data=[]),
            dcc.Store(id='dataset-info-1', data={}),
            dcc.Store(id='dataset-info-2', data={}),
            dcc.Store(id='dataset-info-3', data={}),
        ])

    def setup_callbacks(self):
        """Setup Dash callbacks"""

        # Dataset 1 loading
        @self.app.callback(
            [Output('data-store-1', 'data'),
             Output('skeleton-store-1', 'data'),
             Output('dataset-info-1', 'data'),
             Output('format-display-1', 'children')],
            [Input('load-button-1', 'n_clicks')],
            [State('file-path-input-1', 'value')]
        )
        def load_data_1(n_clicks, file_path):
            if n_clicks > 0 and file_path:
                try:
                    self.load_csv(file_path, 'dataset1')
                    ds = self.datasets['dataset1']
                    sync_info = ""
                    if ds['resampled_data']:
                        sync_info = " | Synchronized"
                    format_text = f"Format: {ds['format_type']} | {ds['frame_count']} frames | {len(ds['point_names'])} points | {ds['sampling_rate']:.1f} Hz{sync_info}"
                    return ds['point_data'], ds['skeleton_connections'], {'frame_count': ds['frame_count'], 'format_type': ds['format_type']}, format_text
                except Exception as e:
                    print(f"Error loading file: {e}")
                    return {}, [], {}, f"Error: {str(e)}"
            return {}, [], {}, ""

        # Dataset 2 loading
        @self.app.callback(
            [Output('data-store-2', 'data'),
             Output('skeleton-store-2', 'data'),
             Output('dataset-info-2', 'data'),
             Output('format-display-2', 'children')],
            [Input('load-button-2', 'n_clicks')],
            [State('file-path-input-2', 'value')]
        )
        def load_data_2(n_clicks, file_path):
            if n_clicks > 0 and file_path:
                try:
                    self.load_csv(file_path, 'dataset2')
                    ds = self.datasets['dataset2']
                    sync_info = ""
                    if ds['resampled_data']:
                        sync_info = " | Synchronized"
                    format_text = f"Format: {ds['format_type']} | {ds['frame_count']} frames | {len(ds['point_names'])} points | {ds['sampling_rate']:.1f} Hz{sync_info}"
                    return ds['point_data'], ds['skeleton_connections'], {'frame_count': ds['frame_count'], 'format_type': ds['format_type']}, format_text
                except Exception as e:
                    print(f"Error loading file: {e}")
                    return {}, [], {}, f"Error: {str(e)}"
            return {}, [], {}, ""

        # Dataset 3 loading
        @self.app.callback(
            [Output('data-store-3', 'data'),
             Output('skeleton-store-3', 'data'),
             Output('dataset-info-3', 'data'),
             Output('format-display-3', 'children')],
            [Input('load-button-3', 'n_clicks')],
            [State('file-path-input-3', 'value')]
        )
        def load_data_3(n_clicks, file_path):
            if n_clicks > 0 and file_path:
                try:
                    self.load_csv(file_path, 'dataset3')
                    ds = self.datasets['dataset3']
                    sync_info = ""
                    if ds['resampled_data']:
                        sync_info = " | Synchronized"
                    format_text = f"Format: {ds['format_type']} | {ds['frame_count']} frames | {len(ds['point_names'])} points | {ds['sampling_rate']:.1f} Hz{sync_info}"
                    return ds['point_data'], ds['skeleton_connections'], {'frame_count': ds['frame_count'], 'format_type': ds['format_type']}, format_text
                except Exception as e:
                    print(f"Error loading file: {e}")
                    return {}, [], {}, f"Error: {str(e)}"
            return {}, [], {}, ""

        # Update frame slider based on loaded datasets and sync mode
        @self.app.callback(
            [Output('frame-slider', 'max'),
             Output('frame-slider', 'marks')],
            [Input('dataset-info-1', 'data'),
             Input('dataset-info-2', 'data'),
             Input('dataset-info-3', 'data'),
             Input('sync-mode-radio', 'value')]
        )
        def update_frame_slider(info1, info2, info3, sync_mode):
            max_frames = 100

            if sync_mode == 'time':
                # In time-based mode, use the synchronized frame count (lowest sampling rate)
                sync_frame_count = 0
                for dataset_key in ['dataset1', 'dataset2', 'dataset3']:
                    if self.datasets[dataset_key]['resampled_data']:
                        # Get frame count from resampled data
                        first_point = next(iter(self.datasets[dataset_key]['resampled_data'].values()), None)
                        if first_point and 'x' in first_point:
                            sync_frame_count = max(sync_frame_count, len(first_point['x']))

                if sync_frame_count > 0:
                    max_frames = sync_frame_count - 1
            else:
                # Frame-based mode: use maximum frame count
                if info1.get('frame_count', 0) > 0:
                    max_frames = max(max_frames, info1['frame_count'] - 1)
                if info2.get('frame_count', 0) > 0:
                    max_frames = max(max_frames, info2['frame_count'] - 1)
                if info3.get('frame_count', 0) > 0:
                    max_frames = max(max_frames, info3['frame_count'] - 1)

            marks = {i: str(i) for i in range(0, max_frames + 1, max(1, max_frames//10))}
            return max_frames, marks

        # Animation controls
        @self.app.callback(
            [Output('interval-component', 'disabled'),
             Output('animation-state', 'data')],
            [Input('play-button', 'n_clicks'),
             Input('pause-button', 'n_clicks'),
             Input('reset-button', 'n_clicks')],
            [State('animation-state', 'data')]
        )
        def control_animation(play_clicks, pause_clicks, reset_clicks, state):
            if not ctx.triggered:
                return True, state

            button_id = ctx.triggered[0]['prop_id'].split('.')[0]

            if button_id == 'play-button':
                state['playing'] = True
                return False, state
            elif button_id == 'pause-button':
                state['playing'] = False
                return True, state
            elif button_id == 'reset-button':
                state['playing'] = False
                state['current_frame'] = 0
                return True, state

            return True, state

        # Speed control
        @self.app.callback(
            Output('interval-component', 'interval'),
            [Input('speed-slider', 'value')]
        )
        def update_speed(speed_value):
            # Convert speed multiplier to interval (lower interval = faster animation)
            # Base interval is 200ms for 1x speed
            base_interval = 200
            speed_multiplier = speed_value / 100.0  # Convert slider value to multiplier
            interval = max(25, int(base_interval / speed_multiplier))  # Minimum 25ms interval
            return interval

        # Frame animation
        @self.app.callback(
            [Output('frame-slider', 'value'),
             Output('animation-state', 'data', allow_duplicate=True)],
            [Input('interval-component', 'n_intervals')],
            [State('animation-state', 'data'),
             State('frame-slider', 'max')],
            prevent_initial_call=True
        )
        def update_frame(n_intervals, state, max_frame):
            if state['playing']:
                new_frame = (state['current_frame'] + 1) % (max_frame + 1)
                state['current_frame'] = new_frame
                return new_frame, state
            return state['current_frame'], state

        # Main plot update
        @self.app.callback(
            Output('3d-plot', 'figure'),
            [Input('frame-slider', 'value'),
             Input('trail-checkbox', 'value'),
             Input('skeleton-checkbox', 'value'),
             Input('dataset-display-checkbox', 'value'),
             Input('sync-mode-radio', 'value'),
             Input('data-store-1', 'data'),
             Input('data-store-2', 'data'),
             Input('data-store-3', 'data'),
             Input('skeleton-store-1', 'data'),
             Input('skeleton-store-2', 'data'),
             Input('skeleton-store-3', 'data')],
            [State('3d-plot', 'relayoutData')]
        )
        def update_plot(current_frame, trail_enabled, skeleton_enabled, datasets_to_show, sync_mode,
                       data_store_1, data_store_2, data_store_3, skeleton_1, skeleton_2, skeleton_3, relayout_data):

            fig = go.Figure()

            show_trails = 'trails' in trail_enabled if trail_enabled else False
            show_skeleton = 'skeleton' in skeleton_enabled if skeleton_enabled else False

            # Dataset colors
            dataset_colors = {
                'dataset1': px.colors.qualitative.Set1,
                'dataset2': px.colors.qualitative.Set2,
                'dataset3': px.colors.qualitative.Set3
            }

            # Process each dataset
            for dataset_key in datasets_to_show:
                if dataset_key == 'dataset1' and data_store_1:
                    # Use synchronized data if available and in time-based mode
                    if sync_mode == 'time' and self.datasets['dataset1']['resampled_data']:
                        data_store = self.datasets['dataset1']['resampled_data']
                    else:
                        data_store = data_store_1
                    skeleton_connections = skeleton_1
                    colors = dataset_colors['dataset1']
                    dataset_name = "Dataset 1"
                elif dataset_key == 'dataset2' and data_store_2:
                    # Use synchronized data if available and in time-based mode
                    if sync_mode == 'time' and self.datasets['dataset2']['resampled_data']:
                        data_store = self.datasets['dataset2']['resampled_data']
                    else:
                        data_store = data_store_2
                    skeleton_connections = skeleton_2
                    colors = dataset_colors['dataset2']
                    dataset_name = "Dataset 2"
                elif dataset_key == 'dataset3' and data_store_3:
                    # Use synchronized data if available and in time-based mode
                    if sync_mode == 'time' and self.datasets['dataset3']['resampled_data']:
                        data_store = self.datasets['dataset3']['resampled_data']
                    else:
                        data_store = data_store_3
                    skeleton_connections = skeleton_3
                    colors = dataset_colors['dataset3']
                    dataset_name = "Dataset 3"
                else:
                    continue

                # Add skeleton wireframe
                if show_skeleton and skeleton_connections:
                    for point1, point2 in skeleton_connections:
                        if point1 in data_store and point2 in data_store:
                            if (current_frame < len(data_store[point1]['x']) and
                                current_frame < len(data_store[point2]['x'])):

                                x1 = data_store[point1]['x'][current_frame]
                                y1 = data_store[point1]['y'][current_frame]
                                z1 = data_store[point1]['z'][current_frame]

                                x2 = data_store[point2]['x'][current_frame]
                                y2 = data_store[point2]['y'][current_frame]
                                z2 = data_store[point2]['z'][current_frame]

                                if all(val is not None for val in [x1, y1, z1, x2, y2, z2]):
                                    if dataset_key == 'dataset1':
                                        line_color = 'blue'
                                    elif dataset_key == 'dataset2':
                                        line_color = 'red'
                                    else:  # dataset3
                                        line_color = 'green'
                                    fig.add_trace(go.Scatter3d(
                                        x=[x1, x2],
                                        y=[y1, y2],
                                        z=[z1, z2],
                                        mode='lines',
                                        line=dict(color=line_color, width=4),
                                        name=f'{dataset_name} Skeleton',
                                        showlegend=False,
                                        hoverinfo='skip'
                                    ))

                # Add joint points and trails
                for i, (point_name, coords) in enumerate(data_store.items()):
                    color = colors[i % len(colors)]

                    # Get human-readable name for display
                    readable_name = self.get_human_readable_name(point_name)

                    # Get data up to current frame
                    x_data = coords['x'][:current_frame+1] if current_frame < len(coords['x']) else coords['x']
                    y_data = coords['y'][:current_frame+1] if current_frame < len(coords['y']) else coords['y']
                    z_data = coords['z'][:current_frame+1] if current_frame < len(coords['z']) else coords['z']

                    # Filter out None values and zero coordinates (which indicate missing/untracked markers)
                    valid_indices = [j for j, (x, y, z) in enumerate(zip(x_data, y_data, z_data))
                                   if (x is not None and y is not None and z is not None and
                                       not (x == 0.0 and y == 0.0 and z == 0.0))]

                    if valid_indices:
                        x_valid = [x_data[j] for j in valid_indices]
                        y_valid = [y_data[j] for j in valid_indices]
                        z_valid = [z_data[j] for j in valid_indices]

                        # Debug: Print info about valid points (only for frame 0 to avoid spam)
                        if current_frame == 0:
                            total_points = len(x_data)
                            valid_points = len(valid_indices)
                            print(f"  {readable_name} ({point_name}): {valid_points}/{total_points} valid points")

                        # Add trail if enabled
                        if show_trails and len(x_valid) > 1:
                            fig.add_trace(go.Scatter3d(
                                x=x_valid,
                                y=y_valid,
                                z=z_valid,
                                mode='lines',
                                line=dict(color=color, width=2),
                                name=f'{dataset_name} {readable_name} Trail',
                                opacity=0.6,
                                showlegend=False
                            ))

                        # Add current point
                        if x_valid and y_valid and z_valid:
                            # Use different marker symbols for different datasets
                            if dataset_key == 'dataset1':
                                marker_symbol = 'circle'
                            elif dataset_key == 'dataset2':
                                marker_symbol = 'diamond'
                            else:  # dataset3
                                marker_symbol = 'square'

                            fig.add_trace(go.Scatter3d(
                                x=[x_valid[-1]],
                                y=[y_valid[-1]],
                                z=[z_valid[-1]],
                                mode='markers',
                                marker=dict(
                                    size=10,
                                    color=color,
                                    symbol=marker_symbol,
                                    line=dict(color='black', width=1)
                                ),
                                name=f'{dataset_name} {readable_name}',
                                text=[f'{dataset_name} {readable_name}<br>Marker: {point_name}<br>Frame: {current_frame}'],
                                hovertemplate='%{text}<br>X: %{x:.3f}<br>Y: %{y:.3f}<br>Z: %{z:.3f}<extra></extra>'
                            ))

            # Preserve camera position if it exists
            camera_settings = dict(
                eye=dict(x=1.5, y=1.5, z=1.5),
                up=dict(x=0, y=1, z=0)
            )

            if relayout_data and 'scene.camera' in relayout_data:
                camera_settings = relayout_data['scene.camera']

            # Calculate ranges based on pre-calculated bounds from all loaded datasets
            global_bounds = {'x_min': float('inf'), 'x_max': float('-inf'),
                           'y_min': float('inf'), 'y_max': float('-inf'),
                           'z_min': float('inf'), 'z_max': float('-inf')}

            # Find the global bounds across all displayed datasets
            bounds_found = False
            for dataset_key in datasets_to_show:
                if dataset_key in ['dataset1', 'dataset2', 'dataset3']:
                    dataset = self.datasets[dataset_key]
                    if dataset['data_bounds']:
                        bounds = dataset['data_bounds']
                        global_bounds['x_min'] = min(global_bounds['x_min'], bounds['x_min'])
                        global_bounds['x_max'] = max(global_bounds['x_max'], bounds['x_max'])
                        global_bounds['y_min'] = min(global_bounds['y_min'], bounds['y_min'])
                        global_bounds['y_max'] = max(global_bounds['y_max'], bounds['y_max'])
                        global_bounds['z_min'] = min(global_bounds['z_min'], bounds['z_min'])
                        global_bounds['z_max'] = max(global_bounds['z_max'], bounds['z_max'])
                        bounds_found = True

            if bounds_found:
                # Add 10% padding to ranges
                x_padding = (global_bounds['x_max'] - global_bounds['x_min']) * 0.1
                y_padding = (global_bounds['y_max'] - global_bounds['y_min']) * 0.1
                z_padding = (global_bounds['z_max'] - global_bounds['z_min']) * 0.1

                x_range = [global_bounds['x_min'] - x_padding, global_bounds['x_max'] + x_padding]
                y_range = [global_bounds['y_min'] - y_padding, global_bounds['y_max'] + y_padding]
                z_range = [global_bounds['z_min'] - z_padding, global_bounds['z_max'] + z_padding]
            else:
                # Fallback ranges if no data
                x_range = [-1, 1]
                y_range = [0, 1]
                z_range = [-0.5, 0.5]

            # Update layout
            sync_mode_text = "Time-Synchronized" if sync_mode == 'time' else "Frame-Based"
            fig.update_layout(
                title=f'3D Skeletal Animation - Frame {current_frame} ({sync_mode_text} Triple Optitrack View)',
                scene=dict(
                    xaxis_title='X (Side to Side)',
                    yaxis_title='Y (Up/Down)',
                    zaxis_title='Z (Depth)',
                    aspectmode='cube',
                    camera=camera_settings,
                    xaxis=dict(range=x_range),
                    yaxis=dict(range=y_range),
                    zaxis=dict(range=z_range)
                ),
                showlegend=True,
                legend=dict(
                    yanchor="top",
                    y=0.99,
                    xanchor="left",
                    x=0.01
                ),
                uirevision='constant'
            )

            return fig

    def run(self, debug=True, port=8051):
        """Run the Dash app"""
        print(f"🚀 Starting OptiTrack Dashboard on http://127.0.0.1:{port}/")
        self.app.run(debug=debug, port=port, host='127.0.0.1')

# Example usage
if __name__ == '__main__':
    print("🔧 Initializing OptiTrack Dashboard...")
    try:
        dashboard = OptitrackAnimationDashboard()
        print("✅ Dashboard initialized successfully!")
        dashboard.run()
    except Exception as e:
        print(f"❌ Error initializing dashboard: {e}")
        import traceback
        traceback.print_exc()
