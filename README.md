# For Teletone fresh install on new PC

**Note: This system currently does NOT work on Windows 11 systems. Also not that the Save button has not been fixed but it still saves to you drive when you press `CTRL + C`

1. Install both Visual Studio Code (https://code.visualstudio.com/) and Python (https://www.python.org/downloads/)

2. Open Visual Studio Code and click view in the top left to show a drop-down menu. press ``crtl + [`]`` (the symbol to the left of 1 on a keyboard), a terminal window should open on the bottom

3. Near the trop right of the terminal window there will be "powershell" and to the right of that a plus with an arrow pointing down, click the arrow then click `command prompt` 

4. In command prompt, type `pip -v` to check if pip is installed

5. If pip is installed type `pip install xbee` and wait for the install to finish.
  
7. Type `pip install dash` this install will take much longer. You will know when it's done when in the terminal window you see "C:\Users\<USER>\..."

8. After installing both Dash and XBee close then reopen visual studio and bring up the dashboard code. None of the import statements at the top of the code should have any underlining. 

## How to Run TeleTone Dashboard

9. Plug in the XBee labeled "TeleTONE` into the computer's USB's port

10. Open `Device Manager` in th Start Menu. Under "Ports" it should show the new USB device with a COM Number. If this is your first time connecting it to the dashboard it may show up as "FT232 USB UART", please wait 5 minutes for the drivers to install automatically. If it does not work and shows up as an unknown USB serial device, unplug it and plug it again and it should show up as a COM port. 

11. Go to the Code and change it to the correct COM # to the number you saw in Device Manager in the following line of code
```
def __init__(self, port="COM13", baudrate=57600, max_points=200, mock=False, runtime_seconds=None):
```
   
13. To start the program hit the play button in the top right of visual studio.

14. For the first time you run it, a blue warning bar will appear at the top. Click `Manage` > Click `Trust`
![image](https://github.com/user-attachments/assets/d11ccea7-0421-489b-a364-b82e5dbd4610)

15. At the bottom it should show you an IP address which is underlined. Press `CTRL + Left-Click` on the IP Address to open it.

17. It will open a webpage in the default internet browser. You can leave this page on as long as you want. 

18. Turn ON the TeleTone Device
 
19. Press the Red Start Button to start recording data.

20. To close the program you must click into the terminal window and press `CTRL + C`

21. If you need to restart, just restart Visual Studio Code. You can leave the webpage open. 

22. The default file save location for the CSV is "C:\Users\<USER>\"
