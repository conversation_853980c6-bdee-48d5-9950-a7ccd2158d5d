import dash
from dash import dcc, html
from dash.dependencies import Input, Output, State
import plotly.graph_objs as go
from collections import deque
import time
import random
import threading
import serial
from xbee import XBee
import csv  
import signal
import sys
import numpy as np

# Serial parameters
serial_port = "COM6"
baudrate = 57600
serial_conn = None
xbee = None
mock = False  # Set to True for simulated data

# Define units for each sensor
sensor_units = {
    "Angular Velocity": "deg/s",
    "Angular Velocity X SD": "deg/s",
    "Angular Velocity Y SD": "deg/s",
    "Angular Velocity Z SD": "deg/s",
    "Accelerometer X": "m/s²",
    "Accelerometer Y": "m/s²",
    "Accelerometer Z": "m/s²",
}

# Define mapping from sensor names to graph IDs
graph_ids = {
    "Angular Velocity": "graph-angular-velocity",
    "Angular Velocity X SD": "graph-angular-velocity-x-sd",
    "Angular Velocity Y SD": "graph-angular-velocity-y-sd",
    "Angular Velocity Z SD": "graph-angular-velocity-z-sd",
    "Accelerometer X": "graph-accelerometer-x",
    "Accelerometer Y": "graph-accelerometer-y",
    "Accelerometer Z": "graph-accelerometer-z",
    "Boat Animation": "graph-boat-animation",  # Added new graph ID for boat animation
}

# Data storage
max_points = 1000
time_data = deque(maxlen=max_points)  # Time axis
streams = {
    "Angular Velocity": deque(maxlen=max_points),
    "Angular Velocity X SD": deque(maxlen=max_points),
    "Angular Velocity Y SD": deque(maxlen=max_points),
    "Angular Velocity Z SD": deque(maxlen=max_points),
    "Accelerometer X": deque(maxlen=max_points),
    "Accelerometer Y": deque(maxlen=max_points),
    "Accelerometer Z": deque(maxlen=max_points),
}

# Storage for boat animation data
current_roll = 0    # Roll angle (x-axis rotation)
current_pitch = 0   # Pitch angle (z-axis rotation)
roll_history = deque(maxlen=100)   # Keep track of recent roll values for smoother animation
pitch_history = deque(maxlen=100)  # Keep track of recent pitch values

saved_data = []  # To store all collected data
running = True
data_thread = None
app = dash.Dash(__name__)

# Create the 3D boat shape
def create_boat_mesh():
    """Creates a simple 3D boat shape using points and faces."""
    # Define boat points - simplified hull shape
    points = np.array([
        # Bottom of hull (8 points)
        [-3, -1, -0.5],  # 0: rear left bottom
        [3, -1, -0.5],   # 1: front left bottom
        [3, 1, -0.5],    # 2: front right bottom
        [-3, 1, -0.5],   # 3: rear right bottom
        
        # Top of hull (8 points)
        [-3, -1, 0.5],   # 4: rear left top
        [3, -1, 0.5],    # 5: front left top
        [3, 1, 0.5],     # 6: front right top
        [-3, 1, 0.5],    # 7: rear right top
        
        # Ship's bridge/cabin (8 points)
        [-2, -0.6, 0.5],  # 8: rear left cabin bottom
        [0, -0.6, 0.5],   # 9: front left cabin bottom
        [0, 0.6, 0.5],    # 10: front right cabin bottom
        [-2, 0.6, 0.5],   # 11: rear right cabin bottom
        [-2, -0.6, 1.5],  # 12: rear left cabin top
        [0, -0.6, 1.5],   # 13: front left cabin top
        [0, 0.6, 1.5],    # 14: front right cabin top
        [-2, 0.6, 1.5],   # 15: rear right cabin top
    ])
    
    # Define faces using point indices
    hull_faces = [
        # Hull bottom
        [0, 1, 2, 3],
        
        # Hull sides
        [0, 1, 5, 4],  # Left side
        [1, 2, 6, 5],  # Front
        [2, 3, 7, 6],  # Right side
        [3, 0, 4, 7],  # Back
        
        # Hull top
        [4, 5, 6, 7],
        
        # Cabin sides
        [8, 9, 13, 12],   # Left
        [9, 10, 14, 13],  # Front
        [10, 11, 15, 14], # Right
        [11, 8, 12, 15],  # Back
        
        # Cabin top
        [12, 13, 14, 15]
    ]
    
    # Create face coordinates for Plotly mesh3d
    i, j, k = [], [], []
    for face in hull_faces:
        if len(face) == 3:  # Triangle
            i.append(face[0])
            j.append(face[1])
            k.append(face[2])
        elif len(face) == 4:  # Quad - split into 2 triangles
            i.extend([face[0], face[0]])
            j.extend([face[1], face[2]])
            k.extend([face[2], face[3]])
    
    # Extract x, y, z coordinates
    x, y, z = points[:,0], points[:,1], points[:,2]
    
    return x, y, z, i, j, k

# Function to apply rotation to the boat
def rotate_boat(x, y, z, roll_angle, pitch_angle):
    """Apply roll and pitch rotations to the boat coordinates."""
    # Convert angles to radians
    roll_rad = np.radians(roll_angle)
    pitch_rad = np.radians(pitch_angle)
    
    # Create rotation matrices
    # Roll (rotation around x-axis)
    roll_matrix = np.array([
        [1, 0, 0],
        [0, np.cos(roll_rad), -np.sin(roll_rad)],
        [0, np.sin(roll_rad), np.cos(roll_rad)]
    ])
    
    # Pitch (rotation around z-axis)
    pitch_matrix = np.array([
        [np.cos(pitch_rad), -np.sin(pitch_rad), 0],
        [np.sin(pitch_rad), np.cos(pitch_rad), 0],
        [0, 0, 1]
    ])
    
    # Combine points into a single array for matrix multiplication
    points = np.vstack((x, y, z)).T
    
    # Apply rotations
    rotated_points = np.dot(points, roll_matrix.T)
    rotated_points = np.dot(rotated_points, pitch_matrix.T)
    
    # Extract rotated coordinates
    x_rot, y_rot, z_rot = rotated_points[:,0], rotated_points[:,1], rotated_points[:,2]
    
    return x_rot, y_rot, z_rot

def signal_handler(sig, frame):
    """Handle keyboard interrupts (Ctrl+C)"""
    print("\nReceived keyboard interrupt, stopping gracefully...")
    stop()
    sys.exit(0)

def connect_serial():
    """Initialize serial connection with XBee. Returns True if successful, False otherwise."""
    global xbee
    try:
        serial_conn = serial.Serial(
            baudrate=baudrate,
            bytesize=8,
            port=serial_port,
            timeout=5
        )
        xbee = XBee(serial_conn)
        print("Serial connection established.")
        return True
    except Exception as e:
        print("Error connecting to serial port:", e)
        # Reset these to None to be safe
        serial_conn = None
        xbee = None
        return False

def convert_to_signed_int(unsigned_value):
    """Convert 16-bit unsigned integer to signed integer."""
    # Check if the value is already within the signed range
    if unsigned_value <= 0x7FFF:
        return unsigned_value
    # Otherwise convert using two's complement
    return unsigned_value - 0x10000

def serial_read():
    """Reads data from XBee and processes it according to the new payload structure."""
    global mock, xbee, data_thread, serial_conn
    try:
        # Check if XBee is initialized before trying to read
        if xbee is None:
            print("XBee not initialized. Stopping serial reader.")
            mock = True
            # Switch to mock data thread
            if running:
                data_thread = threading.Thread(target=mock_reader_thread, daemon=True)
                data_thread.start()
            return
                
        raw_data = xbee.wait_read_frame()
        data = raw_data["rf_data"]
        hex_data = data.hex()
        data_bytes = [hex_data[i:i + 2] for i in range(0, len(hex_data), 2)]
        data_decimals = [int(byte, 16) for byte in data_bytes]
        
        # Check header (first two bytes should be the same value)
        if len(data_decimals) < 20 or data_decimals[0] != data_decimals[1]:
            print("Invalid packet format: Header mismatch or incomplete data")
            return
                
        # Extract runtime from bytes 2-5 (runtime is 32-bit)
        run_time = (data_decimals[2] << 24 |
                    data_decimals[3] << 16 |
                    data_decimals[4] << 8 |
                    data_decimals[5])
        
        # Extract sensor values based on the specified payload structure
        # Each value is 16-bit (high byte then low byte)
        unsigned_angular_velocity = (data_decimals[6] << 8 | data_decimals[7])
        unsigned_angular_velocity_x_sd = (data_decimals[8] << 8 | data_decimals[9])
        unsigned_angular_velocity_y_sd = (data_decimals[10] << 8 | data_decimals[11])
        unsigned_angular_velocity_z_sd = (data_decimals[12] << 8 | data_decimals[13])
        unsigned_accelerometer_x = (data_decimals[14] << 8 | data_decimals[15])
        unsigned_accelerometer_y = (data_decimals[16] << 8 | data_decimals[17])
        unsigned_accelerometer_z = (data_decimals[18] << 8 | data_decimals[19])
        
        # Convert to signed integers
        angular_velocity = convert_to_signed_int(unsigned_angular_velocity)
        angular_velocity_x_sd = convert_to_signed_int(unsigned_angular_velocity_x_sd)
        angular_velocity_y_sd = convert_to_signed_int(unsigned_angular_velocity_y_sd)
        angular_velocity_z_sd = convert_to_signed_int(unsigned_angular_velocity_z_sd)
        
        # Convert accelerometer values to signed and apply scaling
        accelerometer_x = convert_to_signed_int(unsigned_accelerometer_x) / 100
        accelerometer_y = convert_to_signed_int(unsigned_accelerometer_y) / 100
        accelerometer_z = convert_to_signed_int(unsigned_accelerometer_z) / 100
        
        # Create processed data array
        processed_data = [
            run_time,
            angular_velocity,
            angular_velocity_x_sd,
            angular_velocity_y_sd,
            angular_velocity_z_sd,
            accelerometer_x,
            accelerometer_y,
            accelerometer_z
        ]
        
        # Process the data
        process_data(processed_data)

    except Exception as e:
        if running:  # Only print error if we're still supposed to be running
            print("Error reading serial data:", e)
            # If we can't read from XBee, switch to mock mode
            print("Switching to mock data mode after serial error.")
            mock = True
            xbee = None
            if serial_conn:
                try:
                    serial_conn.close()
                except:
                    pass
                serial_conn = None
            
            # Start mock thread if we're still running
            data_thread = threading.Thread(target=mock_reader_thread, daemon=True)
            data_thread.start()
            # Exit this thread
            return

def mock_serial_read():
    """Simulates data for testing without hardware, including realistic boat motion."""
    if not running:
        return
    
    # Generate time
    runtime = len(time_data) + 1 if time_data else 0
    
    # Create sinusoidal boat motion to simulate waves
    time_factor = runtime / 10.0  # Scale time for slower wave motion
    
    # Generate more realistic boat motion with combinations of sine waves
    roll_primary = 15 * np.sin(time_factor * 0.1)  # Primary roll motion
    roll_secondary = 5 * np.sin(time_factor * 0.25)  # Secondary faster roll
    
    pitch_primary = 10 * np.sin(time_factor * 0.08 + 1.5)  # Primary pitch motion (phase shifted)
    pitch_secondary = 3 * np.sin(time_factor * 0.2)  # Secondary faster pitch
    
    # Combine waves for more natural motion
    roll = roll_primary + roll_secondary
    pitch = pitch_primary + pitch_secondary
    
    # Calculate derived sensor data based on the boat motion
    # Angular velocity would be the change in angle over time
    angular_velocity = int(roll - (roll_history[-1] if roll_history else 0))
    
    # Standard deviations - simulate realistic variation
    angular_velocity_x_sd = int(abs(angular_velocity) + random.randint(-10, 10))
    angular_velocity_y_sd = int(abs(pitch - (pitch_history[-1] if pitch_history else 0)) + random.randint(-10, 10))
    angular_velocity_z_sd = random.randint(-20, 20)
    
    # Simulate accelerometer readings affected by boat motion and gravity
    # When boat tilts, gravity components show up on X and Y accelerometers
    g = 9.8  # gravity in m/s²
    roll_rad = np.radians(roll)
    pitch_rad = np.radians(pitch)
    
    # Calculate gravity component on each axis due to tilt
    accelerometer_x = np.sin(roll_rad) * g + random.uniform(-0.2, 0.2)
    accelerometer_y = np.sin(pitch_rad) * g + random.uniform(-0.2, 0.2)
    accelerometer_z = np.cos(roll_rad) * np.cos(pitch_rad) * g + random.uniform(-0.2, 0.2)
    
    # Update roll and pitch history for animation
    roll_history.append(roll)
    pitch_history.append(pitch)
    
    # Update global variables for boat animation
    global current_roll, current_pitch
    current_roll = roll
    current_pitch = pitch
    
    data = [
        runtime,
        angular_velocity,
        angular_velocity_x_sd,
        angular_velocity_y_sd,
        angular_velocity_z_sd,
        accelerometer_x,
        accelerometer_y,
        accelerometer_z
    ]
    
    process_data(data)
    time.sleep(0.1)  # Simulate delay in data collection

def process_data(data):
    """Process the incoming data and update data structures."""
    if not running:
        return
    
    # Extract data for boat animation
    # Use accelerometer data to estimate boat's roll and pitch
    if len(data) >= 8:
        global current_roll, current_pitch
        
        # Get accelerometer values
        accel_x = data[5]  # X acceleration
        accel_y = data[6]  # Y acceleration 
        accel_z = data[7]  # Z acceleration
        
        # Only update if real data is being used (not in mock mode)
        if not mock:
            # Calculate roll and pitch based on accelerometer data
            # Roll: rotation around X axis, calculated from Y and Z components
            if abs(accel_z) > 0.1:  # Prevent division by zero
                roll = np.degrees(np.arctan2(accel_y, accel_z))
                # Smoothing with weighted average to reduce noise
                current_roll = 0.1 * roll + 0.9 * current_roll
                roll_history.append(current_roll)
            
            # Pitch: rotation around Y axis, calculated from X and Z components
            if abs(accel_z) > 0.1:  # Prevent division by zero
                pitch = np.degrees(np.arctan2(-accel_x, accel_z))
                # Smoothing with weighted average
                current_pitch = 0.1 * pitch + 0.9 * current_pitch
                pitch_history.append(current_pitch)
    
    # Save to permanent storage
    saved_data.append(data)
    
    # Update live data
    time_data.append(data[0])
    for i, key in enumerate(streams.keys()):
        if i + 1 < len(data):  # Make sure we have enough data points
            streams[key].append(data[i + 1])
        else:
            streams[key].append(0)  # Default value if data is missing

def serial_reader_thread():
    """Thread for reading serial data."""
    while running:
        try:
            # If we've switched to mock mode, exit this thread
            if mock or xbee is None:
                print("Serial connection not available. Exiting serial reader thread.")
                return
            
            serial_read()
        except Exception as e:
            if running:  # Only print error if we're still supposed to be running
                print(f"Error in serial reader thread: {e}")
                time.sleep(1)  # Prevent error flooding

def mock_reader_thread():
    """Thread for generating mock data."""
    max_runtime = 60000  # Auto-stop after 60 seconds for mock data (optional)
    while running:
        try:
            # If we've switched back to real mode, exit this thread
            if not mock:
                print("Switched to real data mode. Exiting mock reader thread.")
                return
                
            mock_serial_read()
            
            # Optional: Check if we should auto-stop based on data runtime
            if time_data and time_data[-1] > max_runtime and 'auto_stop_enabled' in globals():
                print(f"Auto-stopping after reaching {max_runtime}ms of data")
                stop()
                break
        except Exception as e:
            if running:
                print(f"Error in mock data thread: {e}")
                time.sleep(1)  # Prevent error flooding

def save_to_file():
    """Saves all collected data to a CSV file."""
    try:
        # Generate a timestamp for the filename
        timestamp = time.strftime("%Y%m%d-%H%M%S")
        filename = f"data_log_{timestamp}.csv"
        
        with open(filename, "w", newline='') as file:
            writer = csv.writer(file)
            # Write header
            header = ["Runtime"] + list(streams.keys())
            writer.writerow(header)
            # Write data
            for data in saved_data:
                writer.writerow(data)
        print(f"Data saved to {filename}")
        return filename
    except Exception as e:
        print(f"Error saving data: {e}")
        return None

def stop():
    """Stops the program gracefully."""
    global running
    if not running:
        print("Already stopped.")
        return
        
    print("Stopping data collection...")
    running = False
    
    # Give threads a moment to finish
    time.sleep(0.5)
    
    # Close serial connection if it exists
    if serial_conn:
        try:
            serial_conn.close()
            print("Serial connection closed.")
        except Exception as e:
            print(f"Error closing serial connection: {e}")
    
    # Save collected data
    filename = save_to_file()
    if filename:
        print(f"Program stopped gracefully. Data saved to {filename}")
        return filename
    else:
        print("Program stopped, but data could not be saved.")
        return None

def create_figure(sensor):
    """Create a Plotly figure for the given sensor."""
    if sensor == "Boat Animation":
        return create_boat_figure()
    
    # Different colors for different sensor types
    color_map = {
        "Angular Velocity": "#2980b9",
        "Angular Velocity X SD": "#c0392b", 
        "Angular Velocity Y SD": "#d35400",
        "Angular Velocity Z SD": "#e74c3c",
        "Accelerometer X": "#27ae60",
        "Accelerometer Y": "#2ecc71", 
        "Accelerometer Z": "#16a085"
    }
    
    color = color_map.get(sensor, "#2980b9")
    
    return {
        'data': [
            {
                'x': list(time_data),
                'y': list(streams[sensor]),
                'type': 'scatter',
                'mode': 'lines',
                'name': sensor,
                'line': {'color': color, 'width': 2}
            }
        ],
        'layout': {
            'xaxis': {
                'title': 'Time (ms)',
                'tickformat': ',d'  # Display full integers with commas as thousands separators
            },
            'yaxis': {
                'title': f'Value ({sensor_units[sensor]})',
                'tickformat': ',d' if 'Angular Velocity' in sensor else '.2f'  # Format differently based on sensor type
            },
            'margin': {'l': 50, 'r': 20, 't': 10, 'b': 50},  # Reduced top margin since title is now above
            'height': 300,
            'paper_bgcolor': 'rgba(0,0,0,0)',
            'plot_bgcolor': 'rgba(240,240,240,0.8)',
            'hovermode': 'closest',
            'uirevision': sensor,  # Keeps zoom level consistent on updates
            'hoverlabel': {
                'bgcolor': 'white',
                'font': {'size': 12}
            },
            'hoverformat': '.2f',  # Format for hover text (2 decimal places)
        }
    }

def create_boat_figure():
    """Create a 3D boat figure that updates based on roll and pitch angles."""
    # Get the boat mesh data
    x, y, z, i, j, k = create_boat_mesh()
    
    # Apply current rotation
    x_rot, y_rot, z_rot = rotate_boat(x, y, z, current_roll, current_pitch)
    
    # Create water surface
    water_x = np.linspace(-5, 5, 10)
    water_y = np.linspace(-5, 5, 10)
    water_X, water_Y = np.meshgrid(water_x, water_y)
    water_Z = np.zeros_like(water_X)  # Flat water at z=0
    
    # Add a bit of wave effect
    if len(time_data) > 0:
        t = time_data[-1] / 1000.0  # Convert to seconds
        water_Z += 0.1 * np.sin(water_X + t) * np.cos(water_Y + t * 0.7)
    
    # Create the figure
    return {
        'data': [
            # The boat
            {
                'type': 'mesh3d',
                'x': x_rot,
                'y': y_rot,
                'z': z_rot,
                'i': i,
                'j': j,
                'k': k,
                'color': '#3498db',  # Blue boat
                'opacity': 0.9,
                'flatshading': True,
                'name': 'Boat'
            },
            # The water surface
            {
                'type': 'surface',
                'x': water_X,
                'y': water_Y,
                'z': water_Z,
                'colorscale': [['0', '#142c3c'], ['1', '#2980b9']],  # Dark blue to light blue
                'showscale': False,
                'opacity': 0.8,
                'name': 'Water'
            }
        ],
        'layout': {
            'title': '',
            'scene': {
                'aspectmode': 'manual',
                'aspectratio': {'x': 1, 'y': 1, 'z': 0.7},
                'camera': {
                    'eye': {'x': 1.5, 'y': 1.5, 'z': 0.8},
                    'up': {'x': 0, 'y': 0, 'z': 1}
                },
                'xaxis': {'range': [-5, 5], 'showgrid': False, 'zeroline': False, 'showticklabels': False},
                'yaxis': {'range': [-5, 5], 'showgrid': False, 'zeroline': False, 'showticklabels': False},
                'zaxis': {'range': [-2, 2], 'showgrid': False, 'zeroline': False, 'showticklabels': False}
            },
            'margin': {'l': 0, 'r': 0, 't': 0, 'b': 0},
            'height': 450,
            'paper_bgcolor': 'rgba(240,240,250,0.5)',
            'uirevision': 'constant',  # Keeps camera position on updates
            'annotations': [
                {
                    'text': f'Roll: {current_roll:.1f}° | Pitch: {current_pitch:.1f}°',
                    'x': 0.5,
                    'y': 0.95,
                    'xref': 'paper',
                    'yref': 'paper',
                    'showarrow': False,
                    'font': {'size': 14, 'color': '#2c3e50'}
                }
            ]
        }
    }

def setup_layout():
    """Set up the Dash app layout with added boat animation."""
    app.layout = html.Div([
        html.H1("XBee IMU Sensor Data Visualization", 
                style={'textAlign': 'center', 'color': '#2c3e50', 'margin-bottom': '30px'}),
        
        # Dashboard information
        html.Div([
            html.H3("Dashboard Information", style={'color': '#2c3e50'}),
            html.Div([
                html.P(f"Sensor: {sensor} ({unit})", style={'margin': '5px 0'})
                for sensor, unit in sensor_units.items()
            ]),
            html.P("Values displayed as properly signed readings from the sensor",
                  style={'fontStyle': 'italic', 'marginTop': '10px'}),
            html.P(f"Data Source: {'Mock Data (Simulated)' if mock else 'Real XBee Sensor'}",
                  style={'fontWeight': 'bold', 'marginTop': '10px', 'color': '#e74c3c' if mock else '#27ae60'})
        ], style={'marginBottom': '20px', 'backgroundColor': '#f8f9fa', 'padding': '15px', 'borderRadius': '5px'}),
        
        # Boat Animation Visualization
        html.Div([
            html.H2("Real-time Boat Motion Visualization", 
                   style={'textAlign': 'center', 'color': '#2c3e50', 'marginBottom': '15px'}),
            html.Div([
                dcc.Graph(id=graph_ids["Boat Animation"])
            ], style={'width': '100%', 'padding': '10px', 'backgroundColor': '#f8f9fa', 'borderRadius': '5px'})
        ], style={'marginBottom': '30px'}),
        
        # Sensor Data Visualizations
        html.Div([
            html.H2("Sensor Data Graphs", 
                   style={'textAlign': 'center', 'color': '#2c3e50', 'marginBottom': '15px'}),
            
            # First row: Angular Velocity
            html.Div([
                html.Div([
                    html.H3(f"Angular Velocity ({sensor_units['Angular Velocity']})", 
                            style={'textAlign': 'center', 'marginBottom': '5px'}),
                    dcc.Graph(id=graph_ids['Angular Velocity'])
                ], style={'width': '100%', 'padding': '10px'}),
            ], style={'display': 'flex', 'flexWrap': 'wrap'}),
            
            # Second row: Angular Velocity Standard Deviations
            html.Div([
                html.Div([
                    html.H3(f"Angular Velocity X SD ({sensor_units['Angular Velocity X SD']})", 
                            style={'textAlign': 'center', 'marginBottom': '5px'}),
                    dcc.Graph(id=graph_ids['Angular Velocity X SD'])
                ], style={'width': '33.33%', 'padding': '10px'}),
                
                html.Div([
                    html.H3(f"Angular Velocity Y SD ({sensor_units['Angular Velocity Y SD']})", 
                            style={'textAlign': 'center', 'marginBottom': '5px'}),
                    dcc.Graph(id=graph_ids['Angular Velocity Y SD'])
                ], style={'width': '33.33%', 'padding': '10px'}),
                
                html.Div([
                    html.H3(f"Angular Velocity Z SD ({sensor_units['Angular Velocity Z SD']})", 
                            style={'textAlign': 'center', 'marginBottom': '5px'}),
                    dcc.Graph(id=graph_ids['Angular Velocity Z SD'])
                ], style={'width': '33.33%', 'padding': '10px'}),
            ], style={'display': 'flex', 'flexWrap': 'wrap'}),
            
            # Third row: Accelerometer data
            html.Div([
                html.Div([
                    html.H3(f"Accelerometer X ({sensor_units['Accelerometer X']})", 
                            style={'textAlign': 'center', 'marginBottom': '5px'}),
                    dcc.Graph(id=graph_ids['Accelerometer X'])
                ], style={'width': '33.33%', 'padding': '10px'}),
                
                html.Div([
                    html.H3(f"Accelerometer Y ({sensor_units['Accelerometer Y']})", 
                            style={'textAlign': 'center', 'marginBottom': '5px'}),
                    dcc.Graph(id=graph_ids['Accelerometer Y'])
                ], style={'width': '33.33%', 'padding': '10px'}),
                
                html.Div([
                    html.H3(f"Accelerometer Z ({sensor_units['Accelerometer Z']})", 
                            style={'textAlign': 'center', 'marginBottom': '5px'}),
                    dcc.Graph(id=graph_ids['Accelerometer Z'])
                ], style={'width': '33.33%', 'padding': '10px'}),
            ], style={'display': 'flex', 'flexWrap': 'wrap'}),
        ], style={'marginBottom': '20px'}),
        
        # Control section
        html.Div([
            html.H2("Controls", style={'textAlign': 'center', 'color': '#2c3e50', 'marginBottom': '15px'}),
            
            html.Div([
                html.Button('Start Collection', id='start-button', 
                            style={'margin': '10px', 'padding': '10px 20px', 'backgroundColor': '#27ae60', 'color': 'white', 'border': 'none', 'borderRadius': '5px'}),
                            
                html.Button('Stop Collection', id='stop-button', 
                            style={'margin': '10px', 'padding': '10px 20px', 'backgroundColor': '#e74c3c', 'color': 'white', 'border': 'none', 'borderRadius': '5px'}),
                            
                html.Button('Save Data', id='save-button', 
                            style={'margin': '10px', 'padding': '10px 20px', 'backgroundColor': '#3498db', 'color': 'white', 'border': 'none', 'borderRadius': '5px'}),
                            
                html.Div(id='status-message', style={'margin': '15px', 'padding': '10px', 'textAlign': 'center', 'fontWeight': 'bold'})
            ], style={'display': 'flex', 'flexWrap': 'wrap', 'justifyContent': 'center', 'alignItems': 'center'}),
        ], style={'marginBottom': '20px', 'backgroundColor': '#f8f9fa', 'padding': '15px', 'borderRadius': '5px'}),
        
        # Data refresh interval
        dcc.Interval(
            id='interval-component',
            interval=100,  # in milliseconds (10 updates per second)
            n_intervals=0
        )
    ])

# Create callback for updating each graph
@app.callback(
    [Output(graph_id, 'figure') for graph_id in graph_ids.values()],
    [Input('interval-component', 'n_intervals')]
)
def update_graphs(n):
    """Update all graphs with new data."""
    return [create_figure(sensor) for sensor in graph_ids.keys()]

# Create callbacks for buttons
@app.callback(
    [Output('status-message', 'children'),
     Output('status-message', 'style')],
    [Input('start-button', 'n_clicks'),
     Input('stop-button', 'n_clicks'),
     Input('save-button', 'n_clicks')],
    [State('status-message', 'children')]
)
def handle_buttons(start_clicks, stop_clicks, save_clicks, current_message):
    """Handle button presses."""
    global running, data_thread, mock
    
    ctx = dash.callback_context
    if not ctx.triggered:
        # No button was pressed
        return "", {'display': 'none'}
    
    button_id = ctx.triggered[0]['prop_id'].split('.')[0]
    
    if button_id == 'start-button' and start_clicks:
        # Start data collection
        if not running:
            running = True
            # Try to connect to serial first
            if not mock and connect_serial():
                data_thread = threading.Thread(target=serial_reader_thread, daemon=True)
            else:
                mock = True
                data_thread = threading.Thread(target=mock_reader_thread, daemon=True)
            
            data_thread.start()
            return "Data collection started", {'margin': '15px', 'padding': '10px', 'textAlign': 'center', 'fontWeight': 'bold', 'color': '#27ae60'}
        else:
            return "Data collection already running", {'margin': '15px', 'padding': '10px', 'textAlign': 'center', 'fontWeight': 'bold', 'color': '#f39c12'}
    
    elif button_id == 'stop-button' and stop_clicks:
        # Stop data collection
        if running:
            filename = stop()
            if filename:
                return f"Data collection stopped. Data saved to {filename}", {'margin': '15px', 'padding': '10px', 'textAlign': 'center', 'fontWeight': 'bold', 'color': '#e74c3c'}
            else:
                return "Data collection stopped, but save failed", {'margin': '15px', 'padding': '10px', 'textAlign': 'center', 'fontWeight': 'bold', 'color': '#e74c3c'}
        else:
            return "Data collection not running", {'margin': '15px', 'padding': '10px', 'textAlign': 'center', 'fontWeight': 'bold', 'color': '#f39c12'}
    
    elif button_id == 'save-button' and save_clicks:
        # Save data without stopping collection
        filename = save_to_file()
        if filename:
            return f"Current data saved to {filename}", {'margin': '15px', 'padding': '10px', 'textAlign': 'center', 'fontWeight': 'bold', 'color': '#3498db'}
        else:
            return "Failed to save data", {'margin': '15px', 'padding': '10px', 'textAlign': 'center', 'fontWeight': 'bold', 'color': '#e74c3c'}
    
    # Default - no action taken
    return current_message, {'margin': '15px', 'padding': '10px', 'textAlign': 'center', 'fontWeight': 'bold'}

# Add additional components to make the application run
if __name__ == '__main__':
    # Register signal handler for Ctrl+C
    signal.signal(signal.SIGINT, signal_handler)
    
    # Initially try to connect to real hardware
    if not mock:
        if not connect_serial():
            print("Failed to connect to XBee. Switching to mock data mode.")
            mock = True
    
    # Set up the layout
    setup_layout()
    
    # Start data thread
    running = True
    if not mock:
        data_thread = threading.Thread(target=serial_reader_thread, daemon=True)
    else:
        data_thread = threading.Thread(target=mock_reader_thread, daemon=True)
    data_thread.start()
    
    # Run the app
    print("Starting Dash application...")
    app.run_server(debug=True, use_reloader=False)  # Disable reloader as it would duplicate threads