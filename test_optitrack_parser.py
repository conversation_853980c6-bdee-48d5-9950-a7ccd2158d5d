#!/usr/bin/env python3

def parse_optitrack_csv(filepath):
    """Manual parser for OptiTrack CSV files"""
    print(f"Loading OptiTrack CSV: {filepath}")
    
    # Manual parsing approach for better control
    with open(filepath, 'r') as file:
        lines = file.readlines()
    
    print(f"Total lines in file: {len(lines)}")
    
    # Parse each line manually to handle the specific OptiTrack format
    parsed_lines = []
    for line in lines:
        # Split by comma and clean up
        row_data = [cell.strip() for cell in line.strip().split(',')]
        parsed_lines.append(row_data)
    
    # OptiTrack CSV structure (0-indexed):
    # Row 0: Metadata
    # Row 1: Empty
    # Row 2: Type row (all "Marker")
    # Row 3: Name row (marker names like <PERSON>V7, <PERSON><PERSON><PERSON>, etc.)
    # Row 4: ID row
    # Row 5: Position row
    # Row 6: Headers (Frame, Time (Seconds), X, Y, Z, X, Y, Z, ...)
    # Row 7+: Data
    
    if len(parsed_lines) < 8:
        raise ValueError("File doesn't have enough rows for OptiTrack format")
    
    # Extract marker names from row 3 (name row)
    name_row = parsed_lines[3]
    print(f"Name row: {name_row[:10]}...")  # Show first 10 for debugging
    
    # Extract headers from row 6
    header_row = parsed_lines[6]
    print(f"Header row: {header_row[:10]}...")  # Show first 10 for debugging
    
    # Parse marker names and create mapping
    # Start from column 2 (skip Frame and Time columns)
    point_names = []
    point_column_mapping = {}
    
    # Group columns by marker (every 3 columns = X,Y,Z for one marker)
    col_idx = 2  # Start after Frame and Time columns
    while col_idx < len(name_row):
        if col_idx < len(name_row) and name_row[col_idx]:
            marker_name = name_row[col_idx]
            if marker_name and marker_name not in point_names:
                point_names.append(marker_name)
                
                # Map the next 3 columns to X, Y, Z for this marker
                if col_idx < len(header_row):
                    point_column_mapping[col_idx] = (marker_name, 'x')
                if col_idx + 1 < len(header_row):
                    point_column_mapping[col_idx + 1] = (marker_name, 'y')
                if col_idx + 2 < len(header_row):
                    point_column_mapping[col_idx + 2] = (marker_name, 'z')
        
        col_idx += 3  # Move to next marker (skip X, Y, Z columns)
    
    print(f"Found markers: {point_names}")
    print(f"Point-column mapping created for {len(point_column_mapping)} columns")
    
    # Initialize data structure
    data = {}
    for point_name in point_names:
        data[point_name] = {'x': [], 'y': [], 'z': []}
    
    # Extract data starting from row 7 (data rows)
    frame_count = 0
    data_start_row = 7
    
    for i in range(data_start_row, len(parsed_lines)):
        row = parsed_lines[i]
        
        # Skip empty rows
        if not row or not row[0]:
            continue
        
        # Skip rows with invalid frame numbers
        try:
            frame_num = float(row[0]) if row[0] else None
            if frame_num is None:
                continue
        except (ValueError, IndexError):
            continue
        
        # Process each mapped column for this frame
        for col_idx, (point_name, coord) in point_column_mapping.items():
            if col_idx < len(row) and row[col_idx]:
                try:
                    value = float(row[col_idx])
                except (ValueError, TypeError):
                    value = 0.0
            else:
                value = 0.0
            
            data[point_name][coord].append(value)
        
        frame_count += 1
    
    print(f"Loaded {len(point_names)} points with {frame_count} frames")
    
    # Verify data integrity
    for point_name in point_names:
        x_len = len(data[point_name]['x'])
        y_len = len(data[point_name]['y'])
        z_len = len(data[point_name]['z'])
        print(f"{point_name}: X={x_len}, Y={y_len}, Z={z_len} frames")
        
        # Show first few data points
        if x_len > 0:
            print(f"  First point: ({data[point_name]['x'][0]:.3f}, {data[point_name]['y'][0]:.3f}, {data[point_name]['z'][0]:.3f})")
    
    return data, point_names, frame_count

if __name__ == "__main__":
    # Test with the sample file
    try:
        data, point_names, frame_count = parse_optitrack_csv("data/Take 2025-06-04 11.57.30 AM_ABDADD2.csv")
        print(f"\n✅ Successfully parsed OptiTrack CSV!")
        print(f"📊 {len(point_names)} markers, {frame_count} frames")
        print(f"🎯 Markers: {', '.join(point_names)}")
        
        # Test with a few data points
        if point_names and frame_count > 0:
            first_marker = point_names[0]
            print(f"\n📍 Sample data for {first_marker}:")
            for i in range(min(3, frame_count)):
                x = data[first_marker]['x'][i]
                y = data[first_marker]['y'][i]
                z = data[first_marker]['z'][i]
                print(f"  Frame {i}: ({x:.3f}, {y:.3f}, {z:.3f})")
                
    except Exception as e:
        print(f"❌ Error: {e}")
