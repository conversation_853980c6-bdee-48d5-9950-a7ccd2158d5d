import pandas as pd
import plotly.graph_objects as go
import plotly.express as px
from dash import Dash, dcc, html, Input, Output, State, callback_context, ctx
import numpy as np
from collections import defaultdict
import re
from scipy import interpolate

class SkeletalAnimationDashboard:
    def __init__(self):
        self.app = Dash(__name__)
        self.datasets = {
            'dataset1': {
                'data': None,
                'point_data': {},
                'frame_count': 0,
                'point_names': [],
                'skeleton_connections': [],
                'format_type': 'Kinetisense',
                'name': 'Dataset 1',
                'timestamps': [],
                'sampling_rate': None,
                'duration': 0.0,
                'resampled_data': {}
            },
            'dataset2': {
                'data': None,
                'point_data': {},
                'frame_count': 0,
                'point_names': [],
                'skeleton_connections': [],
                'format_type': 'Kinetisense',
                'name': 'Dataset 2',
                'timestamps': [],
                'sampling_rate': None,
                'duration': 0.0,
                'resampled_data': {}
            },
            'dataset3': {
                'data': None,
                'point_data': {},
                'frame_count': 0,
                'point_names': [],
                'skeleton_connections': [],
                'format_type': 'Kinetisense',
                'name': 'Dataset 3',
                'timestamps': [],
                'sampling_rate': None,
                'duration': 0.0,
                'resampled_data': {}
            }
        }
        self.setup_layout()
        self.setup_callbacks()
    

    
    def find_joint_by_patterns(self, patterns, point_names):
        """Find a joint name that matches any of the given patterns (case-insensitive)"""
        for pattern in patterns:
            # Try exact match first
            if pattern in point_names:
                return pattern
            # Try case-insensitive match
            for joint in point_names:
                if joint.lower() == pattern.lower():
                    return joint
            # Try partial match (contains pattern)
            for joint in point_names:
                if pattern.lower() in joint.lower():
                    return joint
        return None
    
    def define_kinetisense_skeleton(self, point_names):
        """Define skeleton connections for Kinetisense format"""
        skeleton_structure = [
            # Main spine chain
            {'from': ['Head'], 'to': ['Neck']},
            {'from': ['Neck'], 'to': ['SpineShoulder']},
            {'from': ['SpineShoulder'], 'to': ['SpineMid']},
            {'from': ['SpineMid'], 'to': ['SpineBase']},
            
            # Shoulders connected to spine
            {'from': ['SpineShoulder'], 'to': ['ShoulderLeft']},
            {'from': ['SpineShoulder'], 'to': ['ShoulderRight']},
            
            # Left arm chain
            {'from': ['ShoulderLeft'], 'to': ['ElbowLeft']},
            {'from': ['ElbowLeft'], 'to': ['WristLeft']},
            
            # Right arm chain
            {'from': ['ShoulderRight'], 'to': ['ElbowRight']},
            {'from': ['ElbowRight'], 'to': ['WristRight']},
            
            # Hips connected to spine
            {'from': ['SpineBase'], 'to': ['HipLeft']},
            {'from': ['SpineBase'], 'to': ['HipRight']},
            
            # Hip connection (pelvis line)
            {'from': ['HipLeft'], 'to': ['HipRight']},
            
            # Left leg chain
            {'from': ['HipLeft'], 'to': ['KneeLeft']},
            {'from': ['KneeLeft'], 'to': ['AnkleLeft']},
            {'from': ['AnkleLeft'], 'to': ['FootLeft']},
            {'from': ['AnkleLeft'], 'to': ['HeelLeft']},
            
            # Right leg chain
            {'from': ['HipRight'], 'to': ['KneeRight']},
            {'from': ['KneeRight'], 'to': ['AnkleRight']},
            {'from': ['AnkleRight'], 'to': ['FootRight']},
            {'from': ['AnkleRight'], 'to': ['HeelRight']},
            
            # Shoulder connection (collar line)
            {'from': ['ShoulderLeft'], 'to': ['ShoulderRight']},
            
            # Foot connections
            {'from': ['FootLeft'], 'to': ['HeelLeft']},
            {'from': ['FootRight'], 'to': ['HeelRight']},
        ]
        
        return self.build_skeleton_connections(skeleton_structure, point_names)

    def resample_dataset_to_time(self, dataset_key, target_timestamps):
        """Resample a dataset to match target timestamps using interpolation"""
        dataset = self.datasets[dataset_key]
        if not dataset['point_data'] or not dataset['timestamps']:
            return {}

        resampled_data = {}
        source_timestamps = np.array(dataset['timestamps'])
        target_timestamps = np.array(target_timestamps)

        for point_name, coords in dataset['point_data'].items():
            resampled_data[point_name] = {'x': [], 'y': [], 'z': []}

            for axis in ['x', 'y', 'z']:
                source_values = np.array(coords[axis])

                # Find valid (non-None) data points
                valid_mask = ~pd.isna(source_values)
                if np.sum(valid_mask) < 2:  # Need at least 2 points for interpolation
                    # Fill with None if insufficient data
                    resampled_data[point_name][axis] = [None] * len(target_timestamps)
                    continue

                valid_times = source_timestamps[valid_mask]
                valid_values = source_values[valid_mask]

                # Interpolate to target timestamps
                try:
                    # Use linear interpolation, extrapolate with nearest values
                    f = interpolate.interp1d(valid_times, valid_values,
                                           kind='linear', bounds_error=False,
                                           fill_value=(valid_values[0], valid_values[-1]))
                    interpolated_values = f(target_timestamps)
                    resampled_data[point_name][axis] = interpolated_values.tolist()
                except Exception as e:
                    print(f"Interpolation failed for {point_name}.{axis}: {e}")
                    resampled_data[point_name][axis] = [None] * len(target_timestamps)

        return resampled_data

    def synchronize_datasets(self):
        """Synchronize datasets by resampling to a common timeline"""
        # Find datasets with valid data
        valid_datasets = []
        for key, dataset in self.datasets.items():
            if dataset['point_data'] and dataset['timestamps']:
                valid_datasets.append(key)

        if len(valid_datasets) < 2:
            return  # Nothing to synchronize

        # Find the dataset with the lowest sampling rate (longest time intervals)
        min_sampling_rate = float('inf')
        reference_dataset = None

        for key in valid_datasets:
            dataset = self.datasets[key]
            if dataset['sampling_rate'] and dataset['sampling_rate'] < min_sampling_rate:
                min_sampling_rate = dataset['sampling_rate']
                reference_dataset = key

        if not reference_dataset:
            return

        # Use the reference dataset's timestamps as the target
        target_timestamps = self.datasets[reference_dataset]['timestamps']

        # Resample all other datasets to match the reference timeline
        for key in valid_datasets:
            if key != reference_dataset:
                print(f"Resampling {key} to match {reference_dataset} timeline...")
                resampled_data = self.resample_dataset_to_time(key, target_timestamps)
                self.datasets[key]['resampled_data'] = resampled_data
                print(f"Resampled {key}: {len(target_timestamps)} synchronized frames")

        # The reference dataset uses its original data
        self.datasets[reference_dataset]['resampled_data'] = self.datasets[reference_dataset]['point_data']

        print(f"Synchronization complete. Reference: {reference_dataset}")

    def build_skeleton_connections(self, skeleton_structure, point_names):
        """Build skeleton connections based on available joints"""
        connections = []
        
        print(f"\nProcessing {len(skeleton_structure)} potential connections...")
        
        for connection_def in skeleton_structure:
            from_joint = self.find_joint_by_patterns(connection_def['from'], point_names)
            to_joint = self.find_joint_by_patterns(connection_def['to'], point_names)
            
            if from_joint and to_joint:
                # Avoid duplicate connections
                if (from_joint, to_joint) not in connections and (to_joint, from_joint) not in connections:
                    connections.append((from_joint, to_joint))
                    print(f"  ✓ Connected: {from_joint} -> {to_joint}")
            else:
                missing = []
                if not from_joint:
                    missing.append(f"from={connection_def['from'][0]}")
                if not to_joint:
                    missing.append(f"to={connection_def['to'][0]}")
                print(f"  ✗ Skipped: {', '.join(missing)} (missing joints)")
        
        print(f"\nSuccessfully created {len(connections)} connections")
        return connections
    
    def load_kinetisense_csv(self, file_path, dataset_key):
        """Load Kinetisense CSV file and parse skeletal data"""
        df = pd.read_csv(file_path)
        df = df.dropna(how='all')

        # Extract point names from headers
        headers = df.columns.tolist()
        point_names = []

        for header in headers:
            if header.endswith(' X'):
                point_name = header[:-2]  # Remove ' X'
                point_names.append(point_name)

        # Remove timestamp and primary value columns if they exist
        point_names = [name for name in point_names if name not in ['Primary Value', 'Timestamp']]

        # Store dataset info
        self.datasets[dataset_key]['point_names'] = point_names
        self.datasets[dataset_key]['skeleton_connections'] = self.define_kinetisense_skeleton(point_names)
        self.datasets[dataset_key]['format_type'] = 'Kinetisense'

        # Initialize data structure
        point_data = {name: {'x': [], 'y': [], 'z': []} for name in point_names}
        timestamps = []

        # Parse data for each frame
        frame_count = 0
        for row_idx, row in df.iterrows():
            frame_valid = False

            # Extract timestamp if available
            timestamp = None
            if 'Timestamp' in row:
                timestamp = row['Timestamp']
            elif 'Time' in row:
                timestamp = row['Time']
            else:
                # Assume 30 FPS if no timestamp (common for Kinetisense)
                timestamp = row_idx / 30.0

            for point_name in point_names:
                x_col = f"{point_name} X"
                y_col = f"{point_name} Y"
                z_col = f"{point_name} Z"

                if x_col in row and y_col in row and z_col in row:
                    x_val = row[x_col]
                    y_val = row[y_col]
                    z_val = row[z_col]

                    if pd.notna(x_val) and pd.notna(y_val) and pd.notna(z_val):
                        point_data[point_name]['x'].append(float(x_val))
                        point_data[point_name]['y'].append(float(y_val))
                        point_data[point_name]['z'].append(float(z_val))
                        frame_valid = True
                    else:
                        point_data[point_name]['x'].append(None)
                        point_data[point_name]['y'].append(None)
                        point_data[point_name]['z'].append(None)

            if frame_valid:
                timestamps.append(timestamp)
                frame_count += 1

        # Calculate sampling rate and duration
        if len(timestamps) > 1:
            try:
                # Convert timestamps to numeric values
                numeric_timestamps = [float(t) if pd.notna(t) else 0.0 for t in timestamps]
                time_diffs = np.diff(numeric_timestamps)
                avg_time_diff = np.mean(time_diffs)
                sampling_rate = 1.0 / avg_time_diff if avg_time_diff > 0 else 30.0
                duration = numeric_timestamps[-1] - numeric_timestamps[0] if numeric_timestamps else 0.0
                timestamps = numeric_timestamps  # Update with numeric values
            except (ValueError, TypeError):
                sampling_rate = 30.0  # Default assumption
                duration = 0.0
                timestamps = list(range(len(timestamps)))  # Use frame indices as timestamps
        else:
            sampling_rate = 30.0  # Default assumption
            duration = 0.0

        self.datasets[dataset_key]['point_data'] = point_data
        self.datasets[dataset_key]['frame_count'] = frame_count
        self.datasets[dataset_key]['timestamps'] = timestamps
        self.datasets[dataset_key]['sampling_rate'] = sampling_rate
        self.datasets[dataset_key]['duration'] = duration

        print(f"Loaded Kinetisense: {frame_count} frames with {len(point_names)} points")
        print(f"  Sampling rate: {sampling_rate:.1f} Hz, Duration: {duration:.2f}s")

    def load_csv(self, file_path, dataset_key):
        """Load Kinetisense CSV file"""
        print(f"Loading Kinetisense CSV: {file_path}")
        self.load_kinetisense_csv(file_path, dataset_key)

        # Trigger synchronization after loading
        self.synchronize_datasets()
    
    def setup_layout(self):
        """Setup the Dash layout with triple dataset support"""
        self.app.layout = html.Div([
            html.H1("3D Skeletal Animation Dashboard - Triple Kinetisense Dataset Support", style={'textAlign': 'center'}),

            # Dataset 1 Controls
            html.Div([
                html.H3("Dataset 1", style={'color': 'blue'}),
                html.Div([
                    html.Label("Kinetisense CSV File Path:"),
                    dcc.Input(
                        id='file-path-input-1',
                        type='text',
                        placeholder='Enter path to Kinetisense CSV file',
                        value='',
                        style={'width': '400px', 'marginRight': '10px'}
                    ),
                    html.Button('Load Dataset 1', id='load-button-1', n_clicks=0),
                    html.Div(id='format-display-1', style={'marginTop': '10px', 'fontStyle': 'italic'})
                ], style={'margin': '10px'}),
            ], style={'border': '2px solid blue', 'margin': '10px', 'padding': '10px', 'borderRadius': '5px'}),

            # Dataset 2 Controls
            html.Div([
                html.H3("Dataset 2", style={'color': 'red'}),
                html.Div([
                    html.Label("Kinetisense CSV File Path:"),
                    dcc.Input(
                        id='file-path-input-2',
                        type='text',
                        placeholder='Enter path to Kinetisense CSV file',
                        value='',
                        style={'width': '400px', 'marginRight': '10px'}
                    ),
                    html.Button('Load Dataset 2', id='load-button-2', n_clicks=0),
                    html.Div(id='format-display-2', style={'marginTop': '10px', 'fontStyle': 'italic'})
                ], style={'margin': '10px'}),
            ], style={'border': '2px solid red', 'margin': '10px', 'padding': '10px', 'borderRadius': '5px'}),

            # Dataset 3 Controls
            html.Div([
                html.H3("Dataset 3", style={'color': 'green'}),
                html.Div([
                    html.Label("Kinetisense CSV File Path:"),
                    dcc.Input(
                        id='file-path-input-3',
                        type='text',
                        placeholder='Enter path to Kinetisense CSV file',
                        value='',
                        style={'width': '400px', 'marginRight': '10px'}
                    ),
                    html.Button('Load Dataset 3', id='load-button-3', n_clicks=0),
                    html.Div(id='format-display-3', style={'marginTop': '10px', 'fontStyle': 'italic'})
                ], style={'margin': '10px'}),
            ], style={'border': '2px solid green', 'margin': '10px', 'padding': '10px', 'borderRadius': '5px'}),
            
            # Display Controls
            html.Div([
                html.Label('Display Options:', style={'fontWeight': 'bold', 'marginRight': '20px'}),
                dcc.Checklist(
                    id='dataset-display-checkbox',
                    options=[
                        {'label': 'Show Dataset 1', 'value': 'dataset1'},
                        {'label': 'Show Dataset 2', 'value': 'dataset2'},
                        {'label': 'Show Dataset 3', 'value': 'dataset3'}
                    ],
                    value=['dataset1'],
                    inline=True,
                    style={'marginRight': '20px'}
                ),

                html.Label('Show Trails:', style={'marginRight': '10px'}),
                dcc.Checklist(
                    id='trail-checkbox',
                    options=[{'label': 'Enable Trails', 'value': 'trails'}],
                    value=[],
                    inline=True,
                    style={'marginRight': '20px'}
                ),

                html.Label('Show Skeleton:', style={'marginRight': '10px'}),
                dcc.Checklist(
                    id='skeleton-checkbox',
                    options=[{'label': 'Enable Wireframe', 'value': 'skeleton'}],
                    value=['skeleton'],
                    inline=True,
                    style={'marginRight': '20px'}
                ),

                html.Label('Sync Mode:', style={'marginRight': '10px'}),
                dcc.RadioItems(
                    id='sync-mode-radio',
                    options=[
                        {'label': 'Time-based', 'value': 'time'},
                        {'label': 'Frame-based', 'value': 'frame'}
                    ],
                    value='time',
                    inline=True,
                ),
            ], style={'margin': '20px', 'textAlign': 'center'}),
            
            # Animation Controls
            html.Div([
                html.Button('Play', id='play-button', n_clicks=0, style={'marginRight': '10px'}),
                html.Button('Pause', id='pause-button', n_clicks=0, style={'marginRight': '10px'}),
                html.Button('Reset', id='reset-button', n_clicks=0, style={'marginRight': '20px'}),
                
                html.Label('Animation Speed:', style={'marginRight': '10px'}),
                dcc.Slider(
                    id='speed-slider',
                    min=50,
                    max=500,
                    value=200,
                    marks={50: '0.5x', 200: '1x', 500: '2.5x'},
                    tooltip={"placement": "bottom", "always_visible": True}
                ),
            ], style={'margin': '20px', 'textAlign': 'center'}),
            
            dcc.Graph(id='3d-plot', style={'height': '600px'}),
            
            html.Div([
                html.Label('Frame:'),
                dcc.Slider(
                    id='frame-slider',
                    min=0,
                    max=100,
                    value=0,
                    marks={},
                    tooltip={"placement": "bottom", "always_visible": True}
                ),
            ], style={'margin': '20px'}),
            
            dcc.Interval(
                id='interval-component',
                interval=200,
                n_intervals=0,
                disabled=True
            ),
            
            # Data stores
            dcc.Store(id='animation-state', data={'playing': False, 'current_frame': 0}),
            dcc.Store(id='data-store-1', data={}),
            dcc.Store(id='data-store-2', data={}),
            dcc.Store(id='data-store-3', data={}),
            dcc.Store(id='skeleton-store-1', data=[]),
            dcc.Store(id='skeleton-store-2', data=[]),
            dcc.Store(id='skeleton-store-3', data=[]),
            dcc.Store(id='dataset-info-1', data={}),
            dcc.Store(id='dataset-info-2', data={}),
            dcc.Store(id='dataset-info-3', data={}),
        ])
    
    def setup_callbacks(self):
        """Setup Dash callbacks"""
        
        # Dataset 1 loading
        @self.app.callback(
            [Output('data-store-1', 'data'),
             Output('skeleton-store-1', 'data'),
             Output('dataset-info-1', 'data'),
             Output('format-display-1', 'children')],
            [Input('load-button-1', 'n_clicks')],
            [State('file-path-input-1', 'value')]
        )
        def load_data_1(n_clicks, file_path):
            if n_clicks > 0 and file_path:
                try:
                    self.load_csv(file_path, 'dataset1')
                    ds = self.datasets['dataset1']
                    sync_info = ""
                    if ds['resampled_data']:
                        sync_info = " | Synchronized"
                    format_text = f"Format: {ds['format_type']} | {ds['frame_count']} frames | {len(ds['point_names'])} points | {ds['sampling_rate']:.1f} Hz{sync_info}"
                    return ds['point_data'], ds['skeleton_connections'], {'frame_count': ds['frame_count'], 'format_type': ds['format_type']}, format_text
                except Exception as e:
                    print(f"Error loading file: {e}")
                    return {}, [], {}, f"Error: {str(e)}"
            return {}, [], {}, ""
        
        # Dataset 2 loading
        @self.app.callback(
            [Output('data-store-2', 'data'),
             Output('skeleton-store-2', 'data'),
             Output('dataset-info-2', 'data'),
             Output('format-display-2', 'children')],
            [Input('load-button-2', 'n_clicks')],
            [State('file-path-input-2', 'value')]
        )
        def load_data_2(n_clicks, file_path):
            if n_clicks > 0 and file_path:
                try:
                    self.load_csv(file_path, 'dataset2')
                    ds = self.datasets['dataset2']
                    format_text = f"Format: {ds['format_type']} | {ds['frame_count']} frames | {len(ds['point_names'])} points"
                    return ds['point_data'], ds['skeleton_connections'], {'frame_count': ds['frame_count'], 'format_type': ds['format_type']}, format_text
                except Exception as e:
                    print(f"Error loading file: {e}")
                    return {}, [], {}, f"Error: {str(e)}"
            return {}, [], {}, ""

        # Dataset 3 loading
        @self.app.callback(
            [Output('data-store-3', 'data'),
             Output('skeleton-store-3', 'data'),
             Output('dataset-info-3', 'data'),
             Output('format-display-3', 'children')],
            [Input('load-button-3', 'n_clicks')],
            [State('file-path-input-3', 'value')]
        )
        def load_data_3(n_clicks, file_path):
            if n_clicks > 0 and file_path:
                try:
                    self.load_csv(file_path, 'dataset3')
                    ds = self.datasets['dataset3']
                    sync_info = ""
                    if ds['resampled_data']:
                        sync_info = " | Synchronized"
                    format_text = f"Format: {ds['format_type']} | {ds['frame_count']} frames | {len(ds['point_names'])} points | {ds['sampling_rate']:.1f} Hz{sync_info}"
                    return ds['point_data'], ds['skeleton_connections'], {'frame_count': ds['frame_count'], 'format_type': ds['format_type']}, format_text
                except Exception as e:
                    print(f"Error loading file: {e}")
                    return {}, [], {}, f"Error: {str(e)}"
            return {}, [], {}, ""
        
        # Update frame slider based on loaded datasets and sync mode
        @self.app.callback(
            [Output('frame-slider', 'max'),
             Output('frame-slider', 'marks')],
            [Input('dataset-info-1', 'data'),
             Input('dataset-info-2', 'data'),
             Input('dataset-info-3', 'data'),
             Input('sync-mode-radio', 'value')]
        )
        def update_frame_slider(info1, info2, info3, sync_mode):
            max_frames = 100

            if sync_mode == 'time':
                # In time-based mode, use the synchronized frame count (lowest sampling rate)
                sync_frame_count = 0
                for dataset_key in ['dataset1', 'dataset2', 'dataset3']:
                    if self.datasets[dataset_key]['resampled_data']:
                        # Get frame count from resampled data
                        first_point = next(iter(self.datasets[dataset_key]['resampled_data'].values()), None)
                        if first_point and 'x' in first_point:
                            sync_frame_count = max(sync_frame_count, len(first_point['x']))

                if sync_frame_count > 0:
                    max_frames = sync_frame_count - 1
            else:
                # Frame-based mode: use maximum frame count
                if info1.get('frame_count', 0) > 0:
                    max_frames = max(max_frames, info1['frame_count'] - 1)
                if info2.get('frame_count', 0) > 0:
                    max_frames = max(max_frames, info2['frame_count'] - 1)
                if info3.get('frame_count', 0) > 0:
                    max_frames = max(max_frames, info3['frame_count'] - 1)

            marks = {i: str(i) for i in range(0, max_frames + 1, max(1, max_frames//10))}
            return max_frames, marks
        
        # Animation controls
        @self.app.callback(
            [Output('interval-component', 'disabled'),
             Output('animation-state', 'data')],
            [Input('play-button', 'n_clicks'),
             Input('pause-button', 'n_clicks'),
             Input('reset-button', 'n_clicks')],
            [State('animation-state', 'data')]
        )
        def control_animation(play_clicks, pause_clicks, reset_clicks, state):
            if not ctx.triggered:
                return True, state
            
            button_id = ctx.triggered[0]['prop_id'].split('.')[0]
            
            if button_id == 'play-button':
                state['playing'] = True
                return False, state
            elif button_id == 'pause-button':
                state['playing'] = False
                return True, state
            elif button_id == 'reset-button':
                state['playing'] = False
                state['current_frame'] = 0
                return True, state
            
            return True, state
        
        # Speed control
        @self.app.callback(
            Output('interval-component', 'interval'),
            [Input('speed-slider', 'value')]
        )
        def update_speed(speed_value):
            return speed_value
        
        # Frame animation
        @self.app.callback(
            [Output('frame-slider', 'value'),
             Output('animation-state', 'data', allow_duplicate=True)],
            [Input('interval-component', 'n_intervals')],
            [State('animation-state', 'data'),
             State('frame-slider', 'max')],
            prevent_initial_call=True
        )
        def update_frame(n_intervals, state, max_frame):
            if state['playing']:
                new_frame = (state['current_frame'] + 1) % (max_frame + 1)
                state['current_frame'] = new_frame
                return new_frame, state
            return state['current_frame'], state
        
        # Main plot update
        @self.app.callback(
            Output('3d-plot', 'figure'),
            [Input('frame-slider', 'value'),
             Input('trail-checkbox', 'value'),
             Input('skeleton-checkbox', 'value'),
             Input('dataset-display-checkbox', 'value'),
             Input('sync-mode-radio', 'value'),
             Input('data-store-1', 'data'),
             Input('data-store-2', 'data'),
             Input('data-store-3', 'data'),
             Input('skeleton-store-1', 'data'),
             Input('skeleton-store-2', 'data'),
             Input('skeleton-store-3', 'data')],
            [State('3d-plot', 'relayoutData')]
        )
        def update_plot(current_frame, trail_enabled, skeleton_enabled, datasets_to_show, sync_mode,
                       data_store_1, data_store_2, data_store_3, skeleton_1, skeleton_2, skeleton_3, relayout_data):
            
            fig = go.Figure()
            
            show_trails = 'trails' in trail_enabled if trail_enabled else False
            show_skeleton = 'skeleton' in skeleton_enabled if skeleton_enabled else False
            
            # Dataset colors
            dataset_colors = {
                'dataset1': px.colors.qualitative.Set1,
                'dataset2': px.colors.qualitative.Set2,
                'dataset3': px.colors.qualitative.Set3
            }

            # Process each dataset
            for dataset_key in datasets_to_show:
                if dataset_key == 'dataset1' and data_store_1:
                    # Use synchronized data if available and in time-based mode
                    if sync_mode == 'time' and self.datasets['dataset1']['resampled_data']:
                        data_store = self.datasets['dataset1']['resampled_data']
                    else:
                        data_store = data_store_1
                    skeleton_connections = skeleton_1
                    colors = dataset_colors['dataset1']
                    dataset_name = "Dataset 1"
                elif dataset_key == 'dataset2' and data_store_2:
                    # Use synchronized data if available and in time-based mode
                    if sync_mode == 'time' and self.datasets['dataset2']['resampled_data']:
                        data_store = self.datasets['dataset2']['resampled_data']
                    else:
                        data_store = data_store_2
                    skeleton_connections = skeleton_2
                    colors = dataset_colors['dataset2']
                    dataset_name = "Dataset 2"
                elif dataset_key == 'dataset3' and data_store_3:
                    # Use synchronized data if available and in time-based mode
                    if sync_mode == 'time' and self.datasets['dataset3']['resampled_data']:
                        data_store = self.datasets['dataset3']['resampled_data']
                    else:
                        data_store = data_store_3
                    skeleton_connections = skeleton_3
                    colors = dataset_colors['dataset3']
                    dataset_name = "Dataset 3"
                else:
                    continue
                
                # Add skeleton wireframe
                if show_skeleton and skeleton_connections:
                    for point1, point2 in skeleton_connections:
                        if point1 in data_store and point2 in data_store:
                            if (current_frame < len(data_store[point1]['x']) and 
                                current_frame < len(data_store[point2]['x'])):
                                
                                x1 = data_store[point1]['x'][current_frame]
                                y1 = data_store[point1]['y'][current_frame]
                                z1 = data_store[point1]['z'][current_frame]
                                
                                x2 = data_store[point2]['x'][current_frame]
                                y2 = data_store[point2]['y'][current_frame]
                                z2 = data_store[point2]['z'][current_frame]
                                
                                if all(val is not None for val in [x1, y1, z1, x2, y2, z2]):
                                    if dataset_key == 'dataset1':
                                        line_color = 'blue'
                                    elif dataset_key == 'dataset2':
                                        line_color = 'red'
                                    else:  # dataset3
                                        line_color = 'green'
                                    fig.add_trace(go.Scatter3d(
                                        x=[x1, x2],
                                        y=[y1, y2],
                                        z=[z1, z2],
                                        mode='lines',
                                        line=dict(color=line_color, width=4),
                                        name=f'{dataset_name} Skeleton',
                                        showlegend=False,
                                        hoverinfo='skip'
                                    ))
                
                # Add joint points and trails
                for i, (point_name, coords) in enumerate(data_store.items()):
                    color = colors[i % len(colors)]
                    
                    # Get data up to current frame
                    x_data = coords['x'][:current_frame+1] if current_frame < len(coords['x']) else coords['x']
                    y_data = coords['y'][:current_frame+1] if current_frame < len(coords['y']) else coords['y']
                    z_data = coords['z'][:current_frame+1] if current_frame < len(coords['z']) else coords['z']
                    
                    # Filter out None values
                    valid_indices = [j for j, (x, y, z) in enumerate(zip(x_data, y_data, z_data)) 
                                   if x is not None and y is not None and z is not None]
                    
                    if valid_indices:
                        x_valid = [x_data[j] for j in valid_indices]
                        y_valid = [y_data[j] for j in valid_indices]
                        z_valid = [z_data[j] for j in valid_indices]
                        
                        # Add trail if enabled
                        if show_trails and len(x_valid) > 1:
                            fig.add_trace(go.Scatter3d(
                                x=x_valid,
                                y=y_valid,
                                z=z_valid,
                                mode='lines',
                                line=dict(color=color, width=2),
                                name=f'{dataset_name} {point_name} Trail',
                                opacity=0.6,
                                showlegend=False
                            ))
                        
                        # Add current point
                        if x_valid and y_valid and z_valid:
                            # Use different marker symbols for different datasets
                            if dataset_key == 'dataset1':
                                marker_symbol = 'circle'
                            elif dataset_key == 'dataset2':
                                marker_symbol = 'diamond'
                            else:  # dataset3
                                marker_symbol = 'square'
                            
                            fig.add_trace(go.Scatter3d(
                                x=[x_valid[-1]],
                                y=[y_valid[-1]],
                                z=[z_valid[-1]],
                                mode='markers',
                                marker=dict(
                                    size=10,
                                    color=color,
                                    symbol=marker_symbol,
                                    line=dict(color='black', width=1)
                                ),
                                name=f'{dataset_name} {point_name}',
                                text=[f'{dataset_name} {point_name}<br>Frame: {current_frame}'],
                                hovertemplate='%{text}<br>X: %{x:.3f}<br>Y: %{y:.3f}<br>Z: %{z:.3f}<extra></extra>'
                            ))
            
            # Preserve camera position if it exists
            camera_settings = dict(
                eye=dict(x=1.5, y=1.5, z=1.5),
                up=dict(x=0, y=1, z=0)
            )
            
            if relayout_data and 'scene.camera' in relayout_data:
                camera_settings = relayout_data['scene.camera']
            
            # Update layout
            sync_mode_text = "Time-Synchronized" if sync_mode == 'time' else "Frame-Based"
            fig.update_layout(
                title=f'3D Skeletal Animation - Frame {current_frame} ({sync_mode_text} Triple Dataset View)',
                scene=dict(
                    xaxis_title='X (Side to Side)',
                    yaxis_title='Y (Up/Down)',
                    zaxis_title='Z (Depth)',
                    aspectmode='cube',
                    camera=camera_settings,
                    xaxis=dict(range=[-2, 2]),
                    yaxis=dict(range=[-1, 3]),
                    zaxis=dict(range=[0, 5])
                ),
                showlegend=True,
                legend=dict(
                    yanchor="top",
                    y=0.99,
                    xanchor="left",
                    x=0.01
                ),
                uirevision='constant'
            )
            
            return fig
    
    def run(self, debug=True, port=8050):
        """Run the Dash app"""
        self.app.run(debug=debug, port=port)

# Example usage
if __name__ == '__main__':
    dashboard = SkeletalAnimationDashboard()
    dashboard.run()